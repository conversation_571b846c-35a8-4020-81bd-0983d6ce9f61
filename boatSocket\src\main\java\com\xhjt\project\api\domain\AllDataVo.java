package com.xhjt.project.api.domain;



/**
 * class
 *所有数据的集合
 * <AUTHOR>
 */
public class AllDataVo {
    private String awsData;
    private String compassData;
    private String logData;
    private String attitudeData;
    private String racdu1Data;
    private String racdu2Data;
    private String sbe21Data;
    private String gpsData;
    private String co2Data;
    private String ea600Data;
    private String detectorData;
    private String windData;

    /**
     * 需要强制刷新
     */
    private Integer needRefresh;

    public String getAwsData() {
        return awsData;
    }

    public void setAwsData(String awsData) {
        this.awsData = awsData;
    }

    public String getCompassData() {
        return compassData;
    }

    public void setCompassData(String compassData) {
        this.compassData = compassData;
    }

    public String getLogData() {
        return logData;
    }

    public void setLogData(String logData) {
        this.logData = logData;
    }

    public String getAttitudeData() {
        return attitudeData;
    }

    public void setAttitudeData(String attitudeData) {
        this.attitudeData = attitudeData;
    }

    public String getRacdu1Data() {
        return racdu1Data;
    }

    public void setRacdu1Data(String racdu1Data) {
        this.racdu1Data = racdu1Data;
    }

    public String getRacdu2Data() {
        return racdu2Data;
    }

    public void setRacdu2Data(String racdu2Data) {
        this.racdu2Data = racdu2Data;
    }

    public String getSbe21Data() {
        return sbe21Data;
    }

    public void setSbe21Data(String sbe21Data) {
        this.sbe21Data = sbe21Data;
    }

    public String getGpsData() {
        return gpsData;
    }

    public void setGpsData(String gpsData) {
        this.gpsData = gpsData;
    }

    public String getCo2Data() {
        return co2Data;
    }

    public void setCo2Data(String co2Data) {
        this.co2Data = co2Data;
    }

    public String getEa600Data() {
        return ea600Data;
    }

    public void setEa600Data(String ea600Data) {
        this.ea600Data = ea600Data;
    }

    public String getDetectorData() {
        return detectorData;
    }

    public void setDetectorData(String detectorData) {
        this.detectorData = detectorData;
    }

    public String getWindData() {
        return windData;
    }

    public void setWindData(String windData) {
        this.windData = windData;
    }

    public Integer getNeedRefresh() {
        if(needRefresh == null){
            needRefresh = 0;
        }
        return needRefresh;
    }

    public void setNeedRefresh(Integer needRefresh) {
        this.needRefresh = needRefresh;
    }
}
