package com.xhjt.project.device.controller;

import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.DeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 设备信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/device")
public class DeviceController extends BaseController {

    @Autowired
    private DeviceService deviceService;

    /**
     * 获取设备信息列表
     */
    @GetMapping("/list")
    public AjaxResult list(DeviceEntity device) {
        List<DeviceEntity> list = deviceService.selectDeviceList(device);
        return AjaxResult.success(list);
    }

    /**
     * 获取设备信息列表
     */
    @GetMapping("/queryListBySn/{sn}")
    public AjaxResult queryBySn(@PathVariable String sn) {
        return AjaxResult.success(deviceService.queryListBySn(sn));
    }


    /**
     * 获取设备信息列表
     */
    @GetMapping("/queryListBySnAndCode/{sn}/{code}")
    public AjaxResult queryListBySnAndCode(@PathVariable String sn, @PathVariable String code) {
        return AjaxResult.success(deviceService.selectByCodeAndSn(sn, code));
    }

    /**
     * 根据参数编号获取详细信息
     */
    @GetMapping(value = "/{deviceId}")
    public AjaxResult getInfo(@PathVariable Long deviceId) {
        return AjaxResult.success(deviceService.selectDeviceById(deviceId));
    }

}
