package com.xhjt.framework.security.filter;

import com.xhjt.common.utils.ip.IpUtils;
import com.xhjt.framework.security.LoginUser;
import com.xhjt.framework.security.service.IpAuthenticationToken;
import com.xhjt.framework.security.service.SysPermissionService;
import com.xhjt.project.system.domain.SysUser;
import com.xhjt.project.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * class
 *
 * <AUTHOR>
 */
@Component
public class IpAuthenticationProvider implements AuthenticationProvider {

    private Logger logger = LoggerFactory.getLogger(IpAuthenticationProvider.class);

    private ISysUserService userService;

    private SysPermissionService permissionService;

    Set<String> whitelist = new HashSet<String>();

    /**
     * 身份逻辑验证
     * @param authentication
     * @return
     * @throws AuthenticationException
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {

        IpAuthenticationToken authenticationToken = (IpAuthenticationToken) authentication;

//        String ip = String.valueOf(authenticationToken.getPrincipal());

        String ip = "127.0.0.1";

        logger.info("ip为:----------{}",ip);

        List<SysUser> sysUsers = userService.selectUserByDelFlag("0");

        SysUser user = null;

        for(SysUser sysUser:sysUsers){
            if(sysUser.getBindingIp() != null){
                logger.info("用户名字--------------{}",sysUser.getUserName());
                List<String> ipList = IpUtils.getIpList(sysUser.getBindingIp());
                if(ipList.contains(ip)&&sysUser.getAuthentication().equals("0")){
                    user = sysUser;
                    //break;
                }
            }
        }

        if (user == null) {
            logger.info("无法获取用户信息");
            throw new InternalAuthenticationServiceException("无法获取用户信息");
        }

        LoginUser loginUser = new LoginUser(user, permissionService.getMenuPermission(user));

        IpAuthenticationToken authenticationResult = new IpAuthenticationToken(loginUser, loginUser.getAuthorities());

        authenticationResult.setDetails(authenticationToken.getDetails());

        return authenticationResult;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return IpAuthenticationToken.class.isAssignableFrom(authentication);
    }

    public ISysUserService getUserService() {
        return userService;
    }

    public void setUserService(ISysUserService userService) {
        this.userService = userService;
    }

    public SysPermissionService getPermissionService() {
        return permissionService;
    }

    public void setPermissionService(SysPermissionService permissionService) {
        this.permissionService = permissionService;
    }
}
