package com.xhjt.project.snapshot.mapper;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotLogEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 传输管理
 *
 * <AUTHOR>
 */
public interface SnapshotLogMapper {

    /**
     * 查询传输管理列表
     *
     * @param snapshotLog
     * @return 传输管理集合
     */
    public List<SnapshotLogEntity> selectSnapshotLogList(SnapshotLogEntity snapshotLog);

    /**
     * 查询传输管理信息
     *
     * @param snapshotLog
     * @return 传输管理信息
     */
    public SnapshotLogEntity selectSnapshotLog(SnapshotLogEntity snapshotLog);


    /**
     * 查询最新的快照信息
     * @param sn
     * @return
     */
    public List<SnapshotLogEntity> selectNewest(@Param("sn") String sn);
}
