
// 弹出框
$(document).ready(function () {
    let i = 0;
    let stop = 0;
    let taifeng = 1;
    $("#restore ").click(function () {
        if(stop === 0){
            document.getElementById("restore").title = "开始自动缩放";
            document.getElementById("restore").style =
                " background: url(img/typhoon/start.png); background-repeat: no-repeat; border: none;background-size: 26.8px 26.8px; background-color: #fff; border: 1px solid #ccc;background-size: 95%;background-position: center center;"
            stop = 1;
        }else {
            if (routeCoords.length > 2) {
                map.fitBounds(pathPattern.getBounds());
            }
            document.getElementById("restore").title = "暂停自动缩放";
            document.getElementById("restore").style =
                " background: url(img/typhoon/stop.png); background-repeat: no-repeat; border: none;background-size: 26.8px 26.8px; background-color: #fff; border: 1px solid #ccc;background-size: 95%;background-position: center center;"
            stop = 0;
        }

    });
    $("#closebox").click(function () {
        $(".tf-box").toggle(500);
        // $("#closebox").css("right","0px");
        if(taifeng ===1){
            $("#closebox").animate({right:"-=285px"},500);
            // $("#closebox").css("right","0px");
            taifeng = 0;
        }else {
            $("#closebox").animate({right:"+=285px"},500);
            // $("#closebox").css("right","285px");
            taifeng = 1;
        }
    });

    $(".bgbtn").click(function () {
        if (i == 0) {
            $(this).parents(".suspend").stop();
            $(this).parents(".suspend").animate({ width: "20.39vw" }, 300);
            // stopInterval=setInterval("getNowData();", 1000);
            i = 1;
        } else {
            $(this).parents(".suspend").stop();
            $(this).parents(".suspend").animate({ width: "2.2vw" }, 300);
            // clearInterval(stopInterval);
            i = 0;
        }
    });
    //实时色带刷新
    // if (document.getElementById("select").innerHTML == "show" && $(".but_sty").is(":checked") == true) {
    //     setInterval("sdRedraw();", 1000 * 60 * 5);
    // }
});