let stInterval;
let listTy = 0;
// let laputaHost1 = "http://************/laputa";
let layuiTime=new Date().getFullYear();
//年选择器
laydate.render({
    elem: '#timeYear'
    ,type: 'year'
    ,theme: '#09c'
    ,change: function(value, date){//控件选择完毕后的回调---点击日期、清空、现在、确定均会触发。
        layuiTime = value;
        //查询列表
        tyYear(value,".fs-options")
        // //得结束的日期时间对象，开启范围选择（range: true）才会返回。对象成员同上。
    }
});


$(".lishi-top").click(function () {
    // $(".lishi-main").toggle(500);
    if (listTy === 0) {
        $(".lishi1").animate({bottom:"-2px"},500);
        listTy = 1;
    } else {
        $(".lishi1").animate({bottom:"-118px"},500);
        listTy = 0;
    }
});
//按钮：点击图标显示隐藏
function setActive(obj) {
    $(".btn_weather").each(function (obj, index) {
        if ($(this).attr("is_show") === "1") {
            $(this).addClass("active");
        } else {
            $(this).removeClass("active");
        }
    });
}

// 台风
$("#btn_taifeng").on("click", function (e) {
    e.stopPropagation();

    if ($(this).attr("is_show") != null && $(this).attr("is_show") === "1") {
        $(this).attr("is_show", "0");
        document.getElementById("taifeng_show").style = "display: none;"
        // document.getElementById("suspend").style = "display: block;"
        clearInterval(stInterval);
        hideTyphoonList();
        $("#typhoonbody").empty();
        $("#namelist").empty();
        reDrawFeature(routeCoords, shipName);
    } else {
        // 显示
        $(this).attr("is_show", "1");
        timeYear.value=new Date().getFullYear();
        document.getElementById("taifeng_show").style = "display: block;";
        // document.getElementById("suspend").style = "display: none;"
        //实时
        getTyphoonData(new Date().getFullYear());
        //历史
        tyYear(new Date().getFullYear(),"#selectTy");
        stInterval = setInterval('$("#typhoonbody").empty();$("#namelist").empty();hideTyphoonList();getTyphoonData(new Date().getFullYear());', 1000 * 60*15 );
    }
    setActive($(this));
});
// 点轨迹时删除台风
$("#locus").on("click", function (e) {
    hideTyphoonList();
    $("#btn_taifeng").attr("is_show", "0");
    $("#btn_taifeng").removeClass("active");
    document.getElementById("taifeng_show").style = "display: none;"
});
let count =0;
let tfids = [];
//获取台风数据
function getTyphoonData(year) {
    //获取所有台风编号
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/typhoon/getTyphoonData",
        data: {yearsTime: year},
        dataType: "json",
        success: function (result) {
            let data = result.data;
            if(!isEmpty(data)) {
                for (let i = 0; i < data.length; i++) {
                    if (data[i].isactive === "1") {
                        tfids.push(data[i].tfid);
                        typhoonList(data[i].tfid, Math.round(new Date()));

                    }
                }
                ;
            }
            if(tfids.length>=1){
                for (let i = 0; i < tfids.length; i++) {
                    typhoon(tfids[i]);
                }
            }
            nameSplice();
            spliceList();
        },
        async: false,
        contentType: 'application/json;charset=utf-8',
    });

}
//台风轨迹边界，需要所有的点
var allBound=[];
function typhoonList(tfid, endTime) {
    //获取单个台风的所有信息
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/typhoon/getTyphoonPoint",
        data: {tfId: tfid},
        dataType: "json",
        success: function (result) {
            let data = result.data;
            //所有信息
            let tyPoint = [];
            //需要做一个数据转换，只有经纬度
            let allpoints;
            //获取截止日期之前的数据
            for (let i = 0; i < data[0].points.length; i++) {
                let time = (data[0].points[i].time).replace(/-/g, '/');
                let thisTime = new Date(time).getTime();
                // console.log(thisTime);
                if (thisTime <= endTime) {
                    //之前的数据
                    tyPoint.push(data[0].points[i]);
                }
            }
            allpoints = dataHandler(tyPoint);
            allBound.push(tyPoint);
            //台风头 提示框文字
            let html =  "<div class='popuoCss'><B>" + data[data.length - 1].name + "</B> " + " 时间：" + tyPoint[tyPoint.length - 1].time + "\<br\>" +
                "<B>中心位置：</B>" + tyPoint[tyPoint.length - 1].lng + "°/" + tyPoint[tyPoint.length - 1].lat + "°\<br\>" +
                "<B>风速风力：</B>" + tyPoint[tyPoint.length - 1].speed + "米/每秒," + tyPoint[tyPoint.length - 1].strong + "\<br\>" +
                "<B>中心气压：</B>" + tyPoint[tyPoint.length - 1].pressure + "百帕" + "\<br\>" +
                '<B>移速移向：</B>' + tyPoint[tyPoint.length - 1].movespeed + '公里/小时,' + tyPoint[tyPoint.length - 1].movedirection + '\<br\>';

            // //实时台风头图标
            let typhoonIcon = BM.marker(allpoints[allpoints.length - 1], {
                icon: BM.icon({
                    iconUrl: 'img/typhoon/typhoon.gif',
                    // 大小 x，y
                    iconSize: [40, 40],
                    // 偏移x,y
                    iconAnchor: [15, 18],
                    popupAnchor: [-3, -76],
                }),

            }).addTo(map);
            typhoonIcon.bindTooltip(html).openTooltip();

            //扇形台风圈
            sector(allpoints, tyPoint[tyPoint.length - 1].radius7, typhoonIcon,"七级 ","0.5");
            sector(allpoints, tyPoint[tyPoint.length - 1].radius10, typhoonIcon,"十级","0.7");
            sector(allpoints, tyPoint[tyPoint.length - 1].radius12, typhoonIcon,"十二级","1");
            //绘制蓝色台风路径
            let tfPolyline = BM.polyline(allpoints, {color: "#588AF6",}).addTo(map);

            //只画一次 列表名
            nameSplice(data[0],tyPoint);

            //画点
            circleMarker(data, tyPoint, allpoints);
            //存入数组，方便遍历清除
            markers.push(tfPolyline,typhoonIcon);
            // 将地图定位到台风部分所在位置
            map.fitBounds(allBound);
        },
        async: false,
        contentType: 'application/json;charset=utf-8',
    });
}

$("#tyBut").on("click", function (e) {


    //选中的所有值
    let idList = [].map.call($('.fs-option.selected'),
        function(el) {
            return el.dataset.value
        });
    if(idList.length>4){
        alert("最多可选四个");
    }else {
        //点击时清空之前的
        tfids=[];
        allBound=[];
        hideTyphoonList();
        $("#typhoonbody").empty();
        $("#namelist").empty();
        // console.info(layuiTime)
        if(idList.length>0&&idList.length<=4&&$(".fs-label").text()!=="请选择"){

            for (let i = 0; i < idList.length; i++){
                tfids.push(idList[i]);
                typhoonList(tfids[i], Math.round(new Date()));
            }

            //历史列表
            typhoon(idList[idList.length-1]);
        }else {
            //没选择台风但点击获取按钮时
            nameSplice();
            $("#typhoonbody").append('<img src="img/typhoon/noyt.png" style="max-width: 273px;margin-left: 2px;z-index: 856">');

        }
    }

});
//时间格式化
function timeFormat(time) {
    if (!time) return "";
    let date = new Date(time);
    // let yyyy = date.getFullYear();
    let mm = date.getMonth() + 1;
    let dd = date.getDate();
    let hours = date.getHours()
    return `${mm}月${dd}日 ${hours}时`;
}
let tableName = "";
//台风名拼接
function nameSplice(data,tyPoint) {
    tableName = "";

    //拼接列表
    if(isEmpty(data)){
        // console.log(data);
        tableName+='<li class="namelist-select" style="width: 279px; text-align: left; line-height: 43px; font-weight: bold;">\n' +
            '<img src="img/typhoon/announce.gif" style="vertical-align: -3px;" />&nbsp;&nbsp;<span id="nametab">当前西太平洋无台风</span>\n' +
            '</li>';
    }else {
        if(data.isactive==="0"){
            tableName +='<li id=\''+data.tfid+'\' >' +
                '<a  onclick="closes( \''+data.tfid+'\')"><img src="img/typhoon/tfbox/closen.png"  ></a>' +
                '<i style="background:#0099CC;border-radius: 50%;" onclick="DrawTyphoonPathWithOutPlaying(\' '+data.tfid+'\')">' +
                '<br><span style="position: absolute; margin-top: -4px;margin-left: -48px;">'+data.name+'</span><br>' +
                '<span style="position: absolute; margin-top: -4px;margin-left: -56px;">'+tyPoint[tyPoint.length-1].strong+'</span>' +
                '</i>' +
                '</li>';
        }else {
            tableName +='<li id=\''+data.tfid+'\'  onclick="DrawTyphoonPathWithOutPlaying(\' '+data.tfid+'\')">' +
                '<i style="background:#0099CC;border-radius: 50%;">' +
                '<img src="img/typhoon/status/refresh.gif" style="width: 14px;" ><br>' +
                '<span style="position: absolute; margin-top: -4px;margin-left: -48px;">'+data.name+'</span><br>' +
                '<span style="position: absolute; margin-top: -4px;margin-left: -56px;">'+tyPoint[tyPoint.length-1].strong+'</span>' +
                '</i>' +
                '</li>';
        }


    }

    $("#namelist").append(tableName);
}

function isEmpty(obj){
    if(typeof obj === "undefined" || obj === null || obj === ""){
        return true;
    }else{
        return false;
    }
}
let tableContent = "";
//鼠标点击其他恢复原状 所需
let clickPoints =[] ;
//历史拼接数据
function spliceList(allPoint,tfid) {
    if(isEmpty(allPoint)){
        // console.log(allPoint);
        tableContent = "";
        tableContent +='<img src="img/typhoon/noyt.png" style="max-width: 273px;margin-left: 2px;">';
    }else {

        clickPoints.length = 0;
        clickPoints.push(allPoint)
        //时间转成毫秒数 在进行降序输出
        allPoint.sort(function(a,b){
            return new Date(a.time) < new Date(b.time) ? 1 : -1
        });
        tableContent = "";
        for (let i = 0; i < allPoint.length; i++) {
            let dateTime = new Date(allPoint[i].time)*1000/1000000;
            //tr 需要唯一标识  点击后颜色改变： background-color: aqua;  id 去除空格
            tableContent += '<tr id=\''+tfid.trim()+dateTime+'\'  onmousedown ="TyphoonPointMouseDown(\'' + allPoint[i].time+'\',\''+tfid + '\')"' +
                'onmouseout ="TyphoonPointMouseOutFromList(this)" onmouseover ="mouseMap(this)"  style="cursor:pointer">' +
                '<td class="td35 tdnormal">' + timeFormat(allPoint[i].time) + '</td>' +
                '<td class="td25 tdnormal">' + allPoint[i].pressure + '</td>' +
                '<td class="td25 tdnormal">' + allPoint[i].power + '</td>' +
                '<td class="td25 tdnormal">' + allPoint[i].movespeed + '</td>' +
                '</tr>';
        }
    }
    $("#typhoonbody").append(tableContent);

}

//年份的所有台风
function tyYear(year,id) {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/typhoon/getTyphoonData",
        data: {yearsTime: year},
        dataType: "json",
        success: function (result) {
            let data = result.data;
            $('#selectTy').empty() ;

            if(!isEmpty(data)) {

                    for (let i = 0; i < data.length; i++) {
                        $('#selectTy').append('<option   value=' + data[i].tfid + ' >' +data[i].tfid+ data[i].name + '</option>');
                    }
                $(".fs-options").css("display","block") ;
                //复选框select
                $('.demo').fSelect();
            }else {
                $(".fs-label").text("请选择");
                $(".fs-options").css("display","none") ;
                // $('.demo').fSelect()
                // $('#selectTy').empty() ;
                // //复选框select
                // $('#selectTy').append('<option >' +"空" + '</option>');
                // $(".fs-wrap").empty() ;
            }
        },
        async: false,
        contentType: 'application/json;charset=utf-8',
    });
}


//按下鼠标按钮时触发
function TyphoonPointMouseDown(endTime,tfid,allPoint) {
    let time =new Date(endTime)*1000/1000;
    //如果多条会导致所有点被清除
    hideTyphoonList();
    // 重画
    $("#namelist").empty();
    typhoonList(tfid, time);
    //点击其他取消变色  先所有取消颜色
    if (document.getElementById(Number(tfid))){
        document.getElementById(Number(tfid)).style="height: 44px;"
    }
    for(let j=0;j<clickPoints[0].length;j++){
        if(document.getElementById(Number(tfid)+''+new Date(clickPoints[0][j].time)*1000/1000000)) {
            if (j % 2 === 0) {
                document.getElementById(Number(tfid) + '' + new Date(clickPoints[0][j].time) * 1000 / 1000000).style = " background-color:transparent !important;"
            } else {
                document.getElementById(Number(tfid) + '' + new Date(clickPoints[0][j].time) * 1000 / 1000000).style = " background-color:#EDEDED !important;"

            }
        }
    }
    if(document.getElementById(Number(tfid)+''+time/1000)){
        document.getElementById(Number(tfid)+''+time/1000).style=" background-color: rgba(35,198,200,0.3) !important;"
    }
    //
    for(let i=0;i<tfids.length;i++){
        if(Number(tfids[i])!== Number(tfid)){
            typhoonList(tfids[i], Math.round(new Date()));
            document.getElementById(Number(tfids[i])).style="height: 40px;"
        }
    }

}

// onmouseover和onmouseout鼠标移入移
function TyphoonPointMouseOutFromList(e) {
    // console.log(e);
    //滚轮放大缩小
    map.scrollWheelZoom.enable()

}
// 关闭历史台风
function closes(tfid) {
    hideTyphoonList();
    for(let i=0;i<$('.fs-option.selected').length;i++){
        if($('.fs-option.selected')[i].dataset.value===tfid){
            $('.fs-option.selected').toggleClass('selected');
            $('.fs-label').html("请选择");
        }
    }
    if(tfids.length>1){
        $("#namelist").empty();
        $("#typhoonbody").empty();
        tfids.splice(tfids.indexOf(tfid),1);
        for (let i = 0; i < tfids.length; i++){
            typhoon(tfids[i]);
            typhoonList(tfids[i], Math.round(new Date()));
        }
    }else {
        tfids=[];
        nameSplice();
        $("#typhoonbody").empty();
        // spliceList()
        $("#typhoonbody").append('<img src="/img/typhoon/noyt.png" style="max-width: 273px;margin-left: 2px;z-index: 856">');
    }
}

function mouseMap() {
    //上下滚动
    map.scrollWheelZoom.disable()
}
//点击名字显示内容
function DrawTyphoonPathWithOutPlaying(tfid) {
    $("#typhoonbody").empty();
    typhoon(tfid);
}
//点击台风图标后触发

function typhoon(tfid){
    //获取单个台风的所有信息
    $.ajax({
        type: "GET",
        url:  laputaHost + "/api/typhoon/getTyphoonPoint",
        data: {tfId: tfid},
        dataType: "json",
        success: function (result) {
            let data = result.data;
            let allPoint = data[0].points;
            //点击更换样式
            if(document.getElementById(Number(tfid))){
                document.getElementById(Number(tfid)).style="height: 44px;"
            }
            for(let i=0;i<tfids.length;i++){
                if(Number(tfids[i])!== Number(tfid)){
                    // console.log(Number(tfid));
                    document.getElementById(Number(tfids[i])).style="height: 40px;"
                }
            }
            spliceList(allPoint,tfid);
        }
    })
}

//清除所有台风轨迹
function hideTyphoonList() {
    $("#namelist").empty();
    for (let i = 0; i < markers.length; i++) {
        markers[i].remove()
    }
}

//定义数据转换的函数
function dataHandler(tyPoint) {
    //获取台风坐标点数据对象
    let forecast = tyPoint;
    //定义折线点数据的新数组
    let polylinePoints = [];
    //找到经纬度数据，存放在新数组中
    for (let i = 0; i < forecast.length; i++) {
        let data = forecast[i];
        polylinePoints.push([Number(data['lat']), Number(data['lng'])]);
    }
    return polylinePoints;
};
var color;
//所有的点、线存储
var markers = [];

//画点
function circleMarker(data, tyPoint, allpoints) {
    // console.log(data[0]);
    // 画点
    for (let i = 0; i < (tyPoint.length)-1; i++) {
        getColor(tyPoint[i].strong);

        if (i === 0) {
            var simarker = BM.circleMarker(allpoints[i], {
                radius: 3, color: color, weight: 3,
                fill: true,
                fillColor: color,
                fillOpacity: 1,
            }).bindTooltip(data[0].name, {permanent: true, offset: [10, 0], direction: 'right'}).addTo(map);
        } else {
            var simarker = BM.circleMarker(allpoints[i], {
                radius: 3, color: color, weight: 3,
                fill: true,
                fillColor: color,
                fillOpacity: 1,
            }).bindTooltip(html).openTooltip().addTo(map);
        }
        html = "<div class='popuoCss'><B>" + data[0].name + "</B> " + " 时间：" + tyPoint[i].time + "\<br\>" +
            "<B>中心位置：</B>" + (data[0].centerlng) * 100 / 100 + "°/" + (data[0].centerlat) * 100 / 100 + "°\<br\>" +
            "<B>风速风力：</B>" + tyPoint[i].speed + "米/每秒," + tyPoint[i+1].strong + "\<br\>" +
            "<B>中心气压：</B>" + tyPoint[i].pressure + "百帕" + "\<br\>" +
            '<B>移速移向：</B>' + tyPoint[i].movespeed + '公里/小时,' + tyPoint[i].movedirection + '\<br\>';
        // "<B>七级半径：</B>" + parseFloat(routeCoords[routeCoords.length - 1][0]).toFixed(6) + "E," + parseFloat(routeCoords[routeCoords.length - 1][1]).toFixed(6) + "N" + "\<br\>" ;


        markers.push(simarker);
        // console.log(tyPoint);
        //最后一个点显示预测轨迹
        if (i === (tyPoint.length - 1)-1) {
            trajectory(i, tyPoint)
        }
    }

}

//最后一个点的四个扇形区域
function sector(allpoints, tyPoint, typhoonIcon,level,opacity) {

    if(!isEmpty(tyPoint)){
        let radiusAll = tyPoint.split("|");
        let ss=radiusAll[2];
        radiusAll.splice(2,1);
        radiusAll.push(ss)

        //扇形的半径
        let basis = 800;
        //扇形图（用于清除）
        let sector;
        for (let i = 0; i < 4; i++) {
            let radius = radiusAll[i];
            // //添加开始的位置
            let path = [allpoints[allpoints.length - 1]];
            let html2 = "<div class='popuoCss'><B>"+level+"风圈</B> " + "\<br\>" +
                "<B>西北：</B>" + tyPoint.split("|")[2] + "km | 东北：" + tyPoint.split("|")[0] + "km\<br\>" +
                "<B>西南：</B>" + tyPoint.split("|")[3] + "km | 东南：" + tyPoint.split("|")[1] + "km\<br\>";
            for (let j = 0; j < 362; j++) {
                //依次计算距离中心点 5000米偏移角度为 i*90+j/4-20 的点
                path.push(BM.GeometryUtil.destination({
                    lat: Number(allpoints[allpoints.length - 1][0]),
                    lng: Number(allpoints[allpoints.length - 1][1])
                }, i * 90 + j / 4 - 2, radius * basis));
            }
            sector = BM.polygon(path, {
                fillColor: "#FDD49F",
                fillOpacity: opacity,
                color: "#FDD49F",
                weight: 1,
                opacity: 0.1
            }).addTo(map);
            sector.bindTooltip(html2).addTo(map);
            // typhoonIcon.bindTooltip(html2).addTo(map);
            markers.push(sector)
            //扇形区域移入事件
            // sector.on("mouseover",function (typhoonIcon) {
            //     typhoonIcon.bindTooltip('Hello').openTooltip();
            // })
        }

    }
}

//获取颜色 strong:气压名
function getColor(strong) {
    switch (strong) {
        case "热带低压":
            color = "#02FF02";
            break;
        case "热带风暴":
            color = "#0264FF";
            break;
        case "强热带风暴":
            color = "#FFFB05";
            break;
        case "台风":
            color = "#FFAC05";
            break;
        case "强台风":
            color = "#F171F9";
            break;
        case "超强台风":
            color = "#FE0202";
            break;
    }
    return color;
}

//预测轨迹
function trajectory(value, tyPoint) {
    let forecastList = [];
    forecastList = eval(tyPoint[value].forecast);
    let countryData = {
        china: [], //中国
        taiwan: [], //中国台湾
        japan: [], //日本
        hongKong: [], //中国香港
        usa: [], //美国
    }
    let foldLineYColor = ['#FF3C4E', '#FF00FE', '#458B00', '#FEBD00', '#04FAF7'];
    for (let i = 0; i < forecastList.length; i++) {
        switch (forecastList[i].tm) {
            case "中国":
                countryData.china = eval(forecastList[i].forecastPoints);
                break;
            case "中国台湾":
                countryData.taiwan = eval(forecastList[i].forecastPoints);
                break;
            case "日本":
                countryData.japan = eval(forecastList[i].forecastPoints);
                break;
            case "中国香港":
                countryData.hongKong = eval(forecastList[i].forecastPoints);
                break;
            case "美国":
                countryData.usa = eval(forecastList[i].forecastPoints);
                break;
        }
    }
    let china = BM.polyline(countryData.china, {color: foldLineYColor[0], dashArray: [8, 5], weight: 1,}).addTo(map);
    let taiwan = BM.polyline(countryData.taiwan, {color: foldLineYColor[1], dashArray: [8, 5], weight: 1}).addTo(map);
    let japan = BM.polyline(countryData.japan, {color: foldLineYColor[2], dashArray: [8, 5], weight: 1.2}).addTo(map);
    let hongKong = BM.polyline(countryData.hongKong, {color: foldLineYColor[3],dashArray: [8, 5],weight: 1}).addTo(map);
    let usa = BM.polyline(countryData.usa, {color: foldLineYColor[4], dashArray: [8, 5], weight: 1}).addTo(map);
    markers.push(china, taiwan, japan, hongKong, usa);
    forecastMark(forecastList)
}

//预测轨迹点
function forecastMark(forecastList) {
    // console.log(forecastList);
    // console.log(forecastList[2].forecastPoints[2]);
    for (let i = 0; i < forecastList.length; i++) {
        for (let j = 0; j < forecastList[i].forecastPoints.length; j++) {
            getColor(forecastList[i].forecastPoints[j].strong);
            var simarker = BM.circleMarker([Number(forecastList[i].forecastPoints[j]['lat']), Number(forecastList[i].forecastPoints[j]['lng'])],
                {
                    radius: 3, color: color, weight: 3,
                    fill: true,
                    fillColor: color,
                    fillOpacity: 1,
                }).bindTooltip(html).openTooltip().addTo(map);
            html = "<div class='popuoCss'><B>" + forecastList[i].tm + "</B> " + " 时间：" + forecastList[i].forecastPoints[j].time + " 预报\<br\>" +
                "<B>当前位置：</B>" + forecastList[i].forecastPoints[j].lng + "°/" + forecastList[i].forecastPoints[j].lat + "°\<br\>" +
                "<B>最大风速：</B>" + forecastList[i].forecastPoints[j].speed + "米/每秒"  + "\<br\>" +
                "<B>中心气压：</B>" + forecastList[i].forecastPoints[j].pressure + "百帕" + "\<br\>" +
                '<B>风力：</B>' + forecastList[i].forecastPoints[j].power + '级以上 \<br\>';

            markers.push(simarker);
        }
    }

}

//折线图层
// var lineLayer;
// //折点 圈圈图层
// var polyLayer;
// 台风标志图层
// var marker;

//动态绘制折线
function animateDrawLine(allpoints) {
    var length = allpoints.length;
    //定义用来存放递增元素的经纬度数据
    var drawPoints = [];
    var count = 0;
    //定时器100ms，动态的塞入坐标数据
    var timer = setInterval(
        function () {
            //循环台风路径中的每个点，设置定时器依次描绘
            if (count < length) {
                drawPoints.push(allpoints[count]);
                count++;
                //清除之前绘制的折线图层
                if (lineLayer && count !== length) {
                    lineLayer.remove();
                    //    map.removeLayer(polyLayer);
                    polyLayer = null;
                    lineLayer = null;
                }
                //清除之前的marker图层
                if (marker && count !== length) {
                    marker.remove();
                    marker = null;
                }
                //最新数据点drawPoints绘制折线
                lineLayer = BM.polyline(drawPoints, {color: '#588AF6'}).addTo(map);
                circleMarker()
                //根据最新的数据组最后一个绘制marker
                if (count === length) {
                    marker.remove();
                    //如果是路径最后一个点，自动弹出信息框
                    marker = BM.marker(drawPoints[length - 1], {
                        icon: BM.icon({
                            iconUrl: 'img/typhoon/typhoon.png',
                            // 大小 x，y
                            iconSize: [40, 40],
                            // 偏移x,y
                            iconAnchor: [15, 18],
                            popupAnchor: [-3, -76],
                        })
                    })
                        .addTo(map)
                        .bindTooltip(
                            // "<b>" +land + "(" +VongfongTestData[0]['tfbh']+")<br><br>"+
                            // "<B>开始时间：</B>"+VongfongTestData[0]['begin_time']+"<br><br>"+
                            // "<B>结束时间：</B>"+VongfongTestData[0]['end_time']
                            "<B>结束时间：</B>" + tyPoint[tyPoint.length - 1].time
                        ).openTooltip();
                } else {
                    //取已绘制点数组中最后一个点，放置台风标志
                    marker = BM.marker(drawPoints[count - 1], {
                        icon: BM.icon({
                            iconUrl: 'img/typhoon/typhoon.png',
                            // 大小 x，y
                            iconSize: [40, 40],
                            // 偏移x,y
                            iconAnchor: [15, 18],
                            popupAnchor: [-3, -76],
                        })
                    })
                        .addTo(map);
                }
            } else {
                //取完数据后清除定时器
                clearInterval(timer);
            }
        }, 100);
}




