package com.xhjt.project.ship.domain;

import com.xhjt.dctcore.commoncore.domain.BaseEntity;

/**
 * 航次站位信息
 *
 * <AUTHOR>
 */
public class CruiseStationEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 航次ID
     */
    private Long cruiseId;

    private String cruiseCode;

    /**
     * 关联的船只
     */
    private String sn;

    private String shipName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 站位编号
     */
    private String stationCode;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCruiseId() {
        return cruiseId;
    }

    public void setCruiseId(Long cruiseId) {
        this.cruiseId = cruiseId;
    }

    public String getCruiseCode() {
        return cruiseCode;
    }

    public void setCruiseCode(String cruiseCode) {
        this.cruiseCode = cruiseCode;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }
}
