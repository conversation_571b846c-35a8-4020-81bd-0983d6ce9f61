package com.xhjt.project.ship.service;

import com.xhjt.project.ship.domain.CruiseStationEntity;

import java.util.List;

/**
 * 航次站位管理 服务层
 *
 * <AUTHOR>
 */
public interface CruiseStationService {
    /**
     * 查询航次站位信息
     *
     * @param id 航次ID
     * @return 航次信息
     */
    public CruiseStationEntity selectCruiseStationById(Long id);

    /**
     * 查询航次站位列表
     *
     * @param cruiseStation 航次信息
     * @return 航次集合
     */
    public List<CruiseStationEntity> selectCruiseStationListByDept(CruiseStationEntity cruiseStation);

    /**
     * 查询列表
     * @param cruiseStation
     * @return
     */
    public List<CruiseStationEntity> selectCruiseStationList(CruiseStationEntity cruiseStation);
    /**
     * 新增航次站位
     *
     * @param cruiseStation 航次信息
     * @return 结果
     */
    public int addCruiseStation(CruiseStationEntity cruiseStation);

    /**
     * 修改航次站位
     *
     * @param cruiseStation 航次信息
     * @return 结果
     */
    public int updateCruiseStation(CruiseStationEntity cruiseStation);

    /**
     * 删除航次站位信息
     *
     * @param cruiseStationId 参数ID
     * @return 结果
     */
    public int deleteCruiseStationById(Long cruiseStationId);

    /**
     * 批量删除参数信息
     *
     * @param ids 需要删除的参数ID
     * @return 结果
     */
    public int deleteCruiseStationByIds(Long[] ids);

}
