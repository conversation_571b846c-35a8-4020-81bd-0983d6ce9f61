package com.xhjt.project.ship.service.impl;

import com.xhjt.project.ship.domain.RedisObjVo;
import com.xhjt.project.ship.mapper.RedisObjMapper;
import com.xhjt.project.ship.service.RedisObjService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @redis
 */
@Service
public class RedisObjServiceImpl implements RedisObjService {
    @Autowired
    private RedisObjMapper redisObjMapper;
    @Override
    public int save(RedisObjVo redisObjVo) {
       return redisObjMapper.save(redisObjVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(RedisObjVo redisObj) {
        return redisObjMapper.update(redisObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RedisObjVo selectRedisObj(RedisObjVo redisObj) {
        return redisObjMapper.selectRedisObj(redisObj);
    }
}
