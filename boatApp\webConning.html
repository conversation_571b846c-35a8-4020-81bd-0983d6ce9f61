<!DOCTYPE html>
<html lang="utf-8">
<head>
    <meta charset="utf-8" />
    <title>智慧船情</title>
    <script src="js/other/jquery/jquery-3.3.1.min.js"></script>
    <link rel="stylesheet" type="text/css" href="css/main/webConning.css" />
    <script src="js/other/up.js"></script>
    <script src="js/other/echarts.min.js"></script>
    <script src="js/common/host.js"></script>
    <script src="js/other/axios.min.js"></script>
    <script src="js/other/jquery/jquery.cookie.js"></script>
    <script src="js/common/commonFun.js"></script>
    <script src="js/common/auth.js"></script>
    <script src="js/main/webConning.js"></script>
</head>

<body>
<div class="background" id="app">
    <img src="img/webConning/webC.png">
    <h1 class="mainTitle">智慧船情</h1>
    <div class="subtns" id="submenuDiv"></div>
    <div class="subtns1" onclick="javascrtpt:window.location.href='index.html'"></div>
    <div class="submenu" style="display: none;">
        <!--<a href="webConning.html">船舶信息显示系统</a>-->
        <a href="show2.html">视频监控</a>
        <a href="mapgis.html">实时轨迹</a>
    </div>

    <div class="flex main jlr">
        <div class="leftWrap">
            <div class="timer flex a-center">
                <p class="leftYmd"></p>
            </div>

            <div class="titles">UTC</div>
            <div class="boxLine1">
                <div class="lineItem flex">
                    <h4>DATE</h4>
                    <p id="dataTime"></p>
                </div>
                <div class="lineItem flex">
                    <h4>TIME</h4>
                    <span style="text-indent: 10%;" id="currentT"></span>
                </div>
            </div>
            <!-- 经纬度 -->
            <div class="titles">POSITION</div>
            <div class="boxLine1">
                <div class="lineItem flex">
                    <h4>LAT</h4>
                    <span id="lat"></span>
                </div>
                <div class="lineItem flex">
                    <h4>LONG</h4>
                    <span id="lon"></span>
                </div>
            </div>

            <div class="titles" style="margin-top: 3.5vw;">ATTITUDE</div>
            <div class="boxLine2 flex jlr">
                <div class="boxLine2_block flex a-center j-center">

                </div>
                <div class="boxLine2_block flex a-center j-center">

                </div>
            </div>


        </div>
        <!--left-->

        <div class="centerWrap">
            <div class="Speed">
                <span>Speed over ground</span>
                <h2 id="groundSpeed"><em>kt</em></h2>
            </div>

            <div class="Speed right">
                <span>Course over ground</span>
                <h2 id="courseSpeed"><sup>°</sup></h2>
            </div>

            <div class="centerTop">
                <div class="centerTopTitle">Rate of turn</div>
                <div class="centerTopProcess"></div>
                <div class="centerTopZZ"></div>
                <div class="centerTopTitle1">
                    <span>Rate of turn</span>
                    <h4 id="herot">°/min</h4>
                </div>
            </div>

            <div class="centerHeading">
                <h3>HEADING</h3>
                <div class="Inputs" id="heading"></div>

                <div class="barKedu">
                    <div class="barKedu_cell">
                        <div class="barKedu_zz"></div>
                        <div class="barKedu_kedu flex" id="barKedu_kedu" style="width: 3280px; transition: all 0.4s;"><!--1080/条-->
                            <div class="keduCell on" style="margin-left: 0px;">
                                <p>0</p>
                            </div>
                            <div class="keduCell">
                                <p class="scale"></p>
                            </div>
                            <div class="keduCell on" style="margin-left: 0px;">
                                <p>0</p>
                            </div>
                            <div class="keduCell">
                                <p class="scale"></p>
                            </div>
                            <div class="keduCell on" style="margin-left: 0px;">
                                <p>0</p>
                            </div>
                            <div class="keduCell">
                                <p class="scale"></p>
                            </div>
                        </div>
                        <div class="barKedu_cell_this"></div>
                    </div>
                </div>
            </div>

            <div class="stewBlcok">
                <h3>STW</h3>
                <div class="Inputs" id="velocity_water">kn</div>
            </div>
            <div class="stewBlcok">
                <h3>LOG</h3>
                <div class="Inputs" id="mileage">nm</div>
            </div>



        </div>
        <!--center-->

        <div class="rightWrap">
            <!-- 数据同步时间 -->
            <div class="timer flex a-center"
                 style="margin-left: 0%; padding-top: -2vw; justify-content: flex-end; margin-right: -2vw; font-size: 1.2vw;">
                <p id="dataTime2"></p>
                <h4 style="margin-left: 2%;" id="currentT2"></h4>
            </div>
            <div class="titles" style="margin-top: 3vw;">WIND</div>
            <div class="directiveBox flex jlr">
                <div class="directiveBox_item" style="margin-top: 2vw;">
                    <h4>Wind True</h4>
                    <div class="flex jlr" style="padding: 0px 10%;">
                        <label class="flex col a-center j-center">
                            SPEED M/S
                            <b id="absWindSpeed">m/s</b>
                        </label>
                        <label class="flex col a-center j-center">
                            DIRECTION
                            <b id="absWindDirection">°</b>
                        </label>
                    </div>


                    <div class="directiveWrap flex a-center j-center">
                        <div class="directivebox">
                            <div class="directiveThat"></div>
                            <div class="directiveText flex a-center j-center col">
                                <p>风向</p>
                                <p id="windDirection">°</p>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="directiveBox_item" style="margin-top: 2vw;">
                    <h4>Wind relative</h4>
                    <div class="flex jlr" style="padding: 0px 10%;">
                        <label class="flex col a-center j-center">
                            SPEED M/S
                            <b id="relWindSpeed">m/s</b>
                        </label>
                        <label class="flex col a-center j-center">
                            DIRECTION
                            <b id="relWindDirection">°</b>
                        </label>
                    </div>


                    <div class="directiveWrap flex a-center j-center">
                        <div class="directivebox">
                            <div class="directiveThat3"></div>
                            <div class="directiveThat2"></div>
                        </div>
                    </div>

                </div>
            </div>

            <div class="titles" style="margin-top: 3.8vw;">DEPTH<span style="margin-left: 1vw;" id="depth">m</span></div>
            <div class="charLinks">
                <div id="main" style="width: 100%; height: 100%;"></div>
            </div>

        </div>
        <!--right-->
    </div>
</div>
<div class="backgroundFooter flex a-center j-center" style="position: relative;top: -2.8vw;font-size: 1vw;">
    自然资源部第二海洋研究所
</div>
</body>

</html>
