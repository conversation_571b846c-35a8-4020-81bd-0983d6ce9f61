package com.xhjt.project.snapshot.controller;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 通道配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/snapshot/channel")
public class SnapshotChannelController extends BaseController {

    @Autowired
    private SnapshotChannelService snapshotChannelService;


    /**
     * 获取信息列表
     */
    @GetMapping("/list/{sn}")
    public AjaxResult list(@PathVariable String sn) {
        SnapshotChannelEntity channelEntity = new SnapshotChannelEntity();
        channelEntity.setSn(sn);
        List<SnapshotChannelEntity> list = snapshotChannelService.selectSnapshotChannelList(channelEntity);
        return AjaxResult.success(list);
    }

    /**
     * 根据通道号获取配置信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(snapshotChannelService.selectSnapshotChannelById(id));
    }
}
