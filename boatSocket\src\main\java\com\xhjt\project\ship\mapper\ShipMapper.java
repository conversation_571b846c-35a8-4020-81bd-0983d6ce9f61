package com.xhjt.project.ship.mapper;

import com.xhjt.project.ship.domain.ShipEntity;

import java.util.List;

/**
 * 船只管理 数据层
 *
 * <AUTHOR>
 */
public interface ShipMapper {

    /**
     * 根据条件判断
     */
    public List<ShipEntity> selectShipByFactor(ShipEntity ship);

    /**
     * 查询船只信息
     *
     * @param ship 船只信息
     * @return 船只信息
     */
    public ShipEntity selectShip(ShipEntity ship);

    /**
     * 查询船只列表
     *
     * @param ship 船只信息
     * @return 船只集合
     */
    public List<ShipEntity> selectShipList(ShipEntity ship);

}