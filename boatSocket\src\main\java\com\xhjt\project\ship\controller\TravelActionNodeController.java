package com.xhjt.project.ship.controller;

import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.ship.domain.TravelActionNodeEntity;
import com.xhjt.project.ship.service.TravelActionNodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 行程动作节点信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/travelActionNode")
public class TravelActionNodeController extends BaseController {

    @Autowired
    private TravelActionNodeService travelActionNodeService;

    /**
     * 获取行程动作节点信息列表
     */
    @GetMapping("/list")
    public AjaxResult list(TravelActionNodeEntity travelActionNode) {
        List<TravelActionNodeEntity> list = travelActionNodeService.selectTravelActionNodeList(travelActionNode);
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 根据参数编号获取详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(travelActionNodeService.selectTravelActionNodeById(id));
    }

}
