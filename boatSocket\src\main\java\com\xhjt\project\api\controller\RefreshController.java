package com.xhjt.project.api.controller;

import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.api.domain.AllDataVo;
import com.xhjt.project.api.service.WebSocketServlet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.websocket.EncodeException;
import javax.websocket.Session;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 刷新有连接的前端，通过websocket
 *
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/api/refresh")
public class RefreshController extends BaseController {

    @Autowired
    private WebSocketServlet webSocketServlet;

    /**
     * 刷新有连接的前端页面
     */
    @GetMapping("/html")
    public AjaxResult refreshHtml() throws IOException, EncodeException {

        ConcurrentHashMap<Session, Map<String, String>> map = webSocketServlet.getSessionMap();

        for (Map.Entry<Session, Map<String, String>> entry : map.entrySet()) {
            AllDataVo allDataVo = new AllDataVo();
            allDataVo.setNeedRefresh(1);
            entry.getKey().getBasicRemote().sendObject(allDataVo);
        }

        return AjaxResult.success("刷新成功");
    }


}
