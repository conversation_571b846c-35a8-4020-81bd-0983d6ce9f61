package com.xhjt.project.ship.service;

import com.xhjt.common.utils.JsonUtil;
import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.common.RedisParameter;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.mapper.ShipMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 船只管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class ShipService {
    @Autowired
    private ShipMapper shipMapper;
    @Autowired
    private RedisTemplate redisTemplate;

    public List<ShipEntity> selectShipByFactor(ShipEntity ship) {
        return shipMapper.selectShipByFactor(ship);
    }

    /**
     * 查询船只信息
     *
     * @param shipId 船只ID
     * @return 船只信息
     */
    public ShipEntity selectShipById(Long shipId) {
        ShipEntity ship = new ShipEntity();
        ship.setShipId(shipId);
        return shipMapper.selectShip(ship);
    }

    /**
     * 查询船只信息
     *
     * @param sn
     * @return 船只信息
     */
    public ShipEntity selectShipBySn(String sn) {
        ShipEntity ship = new ShipEntity();
        ship.setSn(sn);
        return shipMapper.selectShip(ship);
    }

    /**
     * 查询船只列表
     *
     * @param ship 船只信息
     * @return 船只集合
     */
    public List<ShipEntity> selectShipListByDeptId(ShipEntity ship) {
        ship.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        return shipMapper.selectShipList(ship);
    }

    /**
     * 查询船只列表
     *
     * @param ship 船只信息
     * @return 船只集合
     */
    @DataScope(deptAlias = "d")
    public List<ShipEntity> selectShipList(ShipEntity ship) {
        return shipMapper.selectShipList(ship);
    }

    /**
     * 查询船只列表
     *
     * @return 船只集合
     */
    public List<ShipEntity> selectAllShip() {
        return shipMapper.selectShipList(null);
    }
    /**
     * 查询所有启用的
     *
     * @return
     */
    public List<String> queryEnableSnList() {
        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        String snListStr = valueOperations.get(RedisParameter.ALL_ENABLE_SHIP_SN);

        if (StringUtils.isNotBlank(snListStr)) {
            return JsonUtil.object2Obj(snListStr, List.class, String.class);
        }

        return renewEnableSnList();
    }

    /**
     * 如果redis没有就获取数据库
     *
     * @throws Exception
     */
    public List<String> renewEnableSnList() {
        List<String> list = new ArrayList<>();
        List<ShipEntity> snList = selectAllShip();
        if (snList!=null && snList.size() != 0) {
            for (ShipEntity shipEntity:snList) {
                list.add(shipEntity.getSn());
            }
        }
        return list;
    }
}
