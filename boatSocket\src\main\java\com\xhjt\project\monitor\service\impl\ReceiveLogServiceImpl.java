package com.xhjt.project.monitor.service.impl;


import com.xhjt.project.monitor.domain.ReceiveLog;
import com.xhjt.project.monitor.mapper.ReceiveLogMapper;
import com.xhjt.project.monitor.service.IReceiveLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 操作日志 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class ReceiveLogServiceImpl implements IReceiveLogService {

    private static final Logger logger = LoggerFactory.getLogger(ReceiveLogServiceImpl.class);
    @Autowired
    private ReceiveLogMapper receiveLogMapper;


    @Override
    public List<ReceiveLog> selectList(Long recordTime) {
        return receiveLogMapper.selectList(recordTime);
    }
}
