package com.xhjt.project.api.common;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.utils.spring.SpringUtils;
import com.xhjt.project.api.domain.AllDataVo;
import com.xhjt.project.api.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.Session;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * 定时任务
 */
@Component
public class CronSendData implements Runnable {
    private Logger logger = LoggerFactory.getLogger(CronSendData.class);
    private ConcurrentHashMap<Session, Map<String, String>> hashMap;

    public CronSendData() {
    }

    public CronSendData(ConcurrentHashMap<Session, Map<String, String>> hashMap) {
        this.hashMap = hashMap;
    }

    public void renewMap(ConcurrentHashMap<Session, Map<String, String>> hashMap){
        this.hashMap = hashMap;
    }

    @Override
    public void run() {
        RealtimeService realtimeService = SpringUtils.getBean(RealtimeService.class);

        Map<String, Object> map;
        try {
            for (Map.Entry<Session, Map<String, String>> entry : hashMap.entrySet()) {
                String[] codeArr = entry.getValue().get("codes").split(",");
                map = new HashMap<>(codeArr.length);

                for (String code : codeArr) {
                    code = code.trim();
                    Object ob = realtimeService.getLatestData(entry.getValue().get("sn"), code);
                    if (ob != null) {
                        map.put(entry.getValue().get("sn") + "_" + code, ob);
                    }
                }
//                entry.getKey().getBasicRemote().sendText(JSONObject.toJSONString(map));
                sendData(entry.getKey(),map);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private synchronized void sendData(Session session, Map<String, Object> allDataVo) {
        synchronized(session) {
            try {
                session.getBasicRemote().sendText(JSONObject.toJSONString(allDataVo));
            } catch (Exception e) {
                logger.error("websocket发送出错----{}", e);
            }
        }
    }

    /**
     * 查询共同数据
     */
    private void getCommonData(AllDataVo allDataVo, String sn) {
        GpsService gpsService = SpringUtils.getBean(GpsService.class);
//        gps数据
        allDataVo.setGpsData(JSONObject.toJSONString(gpsService.getLatestDataFromHbase(sn, "032A")));
    }

}
