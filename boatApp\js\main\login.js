$(function () {
    logout();

    // $(":button").on("click", function () {
    //
    //     login(username, password);
    // });
    initCaptcha();

    $("body").keydown(function () {
        if (event.keyCode === "13") {
            $(":button").click();
        }
    });
});

function login(username, password, code) {
    $.ajax({
        type: "post",
        url: laputaHost + "/login",
        data: {
            username: username,
            password: password,
            type: 1,
            code: code
        },
        dataType: 'json',
        success: function (data) {
            if (data.code === 200) {
                setToken(data.token);
                window.location.href = 'ship.html';
                // if (history.length === 2) {
                //     window.location.href = 'ship.html';
                // } else {
                //     window.history.back(-1);
                // }
            } else {
                $("#show").html('<span id="red">' + data.msg + '</span>');
            }
        }
    });
}

function logout() {
    $.ajax({
        type: "post",
        url: laputaHost + "/logout",
        dataType: 'json',
        success: function (data) {
            removeToken();
            removeCurrentSn();
        }
    });
}

function initCaptcha() {
    $('#captchaContent').slideVerify({
        //服务器请求地址
        baseUrl: laputaHost,
        //pop模式 必填 被点击之后出现行为验证码的元素id
        containerId: 'loginBtn',
        //展示模式
        mode: 'pop',
        //图片的大小对象,有默认值{ width: '310px',height: '155px'},可省略
        imgSize: {
            width: '400px',
            height: '200px',
        },
        //下方滑块的大小对象,有默认值{ width: '310px',height: '50px'},可省略
        barSize: {
            width: '400px',
            height: '40px',
        },
        //检验参数合法性的函数  mode ="pop"有效
        beforeCheck: function () {
            let username = $("#username").val();
            let password = $("#password").val();
            if (username === '') {
                $("#show").html("<span id='red'>用户名不能为空</span>");
                return false;
            }
            if (password === '') {
                $("#show").html("<span id='red'>密码不能为空</span>");
                return false;
            }
            return true;
        },
        //加载完毕的回调
        ready: function () {
        },
        //成功的回调
        success: function (params) {
            // params为返回的二次验证参数 需要在接下来的实现逻辑回传服务器
            login($("#username").val(), $("#password").val(), params.captchaVerification);
        },
        //失败的回调
        error: function () {
        }
    });
}

