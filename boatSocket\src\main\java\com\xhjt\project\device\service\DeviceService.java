package com.xhjt.project.device.service;

import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.mapper.DeviceMapper;
import com.xhjt.project.ship.service.ShipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class DeviceService {
    @Autowired
    private DeviceMapper deviceMapper;
    @Autowired
    private ShipService shipService;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 查询设备信息
     *
     * @param deviceId 设备ID
     * @return 设备信息
     */
    public DeviceEntity selectDeviceById(Long deviceId) {
        DeviceEntity device = new DeviceEntity();
        device.setId(deviceId);
        return deviceMapper.selectDevice(device);
    }

    /**
     * 查询设备列表
     *
     * @param device 设备信息
     * @return 设备集合
     */
    @DataScope(deptAlias = "d")
    public List<DeviceEntity> selectDeviceList(DeviceEntity device) {
        return deviceMapper.selectDeviceList(device);
    }


    public List<DeviceEntity> queryListBySn(String sn) {
        DeviceEntity device = new DeviceEntity();
        device.setSn(sn);
        return deviceMapper.selectDeviceList(device);
    }

    /**
     * 查询设备列表
     *
     * @return 设备集合
     */
    public List<DeviceEntity> selectAllDevice() {
        return deviceMapper.selectDeviceList(null);
    }

    /**
     * 根据 code查询设备信息
     *
     * @param code
     * @return 设备信息
     */
    public DeviceEntity selectByCodeAndSn(String sn, String code) {
        DeviceEntity device = new DeviceEntity();
        device.setSn(sn);
        device.setCode(code);
        return deviceMapper.selectDevice(device);
    }

    /**
     * 更新redis中设备
     */
    public DeviceEntity getDeviceBySnAndCode(String sn, String code) {
        ValueOperations<String, DeviceEntity> opsForValue = redisTemplate.opsForValue();
        DeviceEntity deviceEntity = opsForValue.get("ALL_DEVICE-" + sn + "_" + code);

        if (deviceEntity != null) {
            return deviceEntity;
        }

        List<DeviceEntity> list = selectAllDevice();
        for (DeviceEntity device : list) {
            opsForValue.set("ALL_DEVICE-" + device.getSn() + "_" + device.getCode(), device);
        }

        return opsForValue.get("ALL_DEVICE-" + sn + "_" + code);
    }

}
