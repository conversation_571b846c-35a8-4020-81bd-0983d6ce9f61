package com.xhjt.project.snapshot.controller;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotLogEntity;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.snapshot.service.SnapshotChannelService;
import com.xhjt.project.snapshot.service.SnapshotLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 快照传输管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/snapshot/log")
public class SnapshotLogController extends BaseController {

    @Autowired
    private SnapshotLogService snapshotLogService;
    @Autowired
    private SnapshotChannelService snapshotChannelService;

    /**
     * 获取信息列表
     */
    @GetMapping("/list/{sn}/{channelCode}/{beginTime}/{endTime}")
    public AjaxResult list(@PathVariable String sn, @PathVariable String channelCode,
                           @PathVariable("beginTime") Long beginTime, @PathVariable("endTime") Long endTime) {
        SnapshotLogEntity logEntity = new SnapshotLogEntity();
        logEntity.setSn(sn);
        logEntity.setChannelCode(channelCode);
        logEntity.setBeginTimeStamp(beginTime);
        logEntity.setEndTimeStamp(endTime);
        List<SnapshotLogEntity> list = snapshotLogService.selectSnapshotLogList(logEntity);
        return AjaxResult.success(list);
    }

    /**
     * 根据id获取配置信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(snapshotLogService.selectSnapshotLogById(id));
    }

    /**
     * 获取信息列表
     */
    @GetMapping("/newest/{sn}")
    public AjaxResult selectNewest(@PathVariable String sn) {
        List<SnapshotLogEntity> snapshotLogEntityList = snapshotLogService.selectNewest(sn);

        SnapshotChannelEntity queryEntity = new SnapshotChannelEntity();
        queryEntity.setSn(sn);
        List<SnapshotChannelEntity> channelEntityList = snapshotChannelService.selectSnapshotChannelList(queryEntity);

        List<SnapshotLogEntity> result = new ArrayList<>();
        for (SnapshotLogEntity snapshotLogEntity : snapshotLogEntityList) {
            boolean have = false;
            for (SnapshotChannelEntity snapshotChannelEntity : channelEntityList) {
                if (snapshotLogEntity.getChannelCode().equals(snapshotChannelEntity.getCode())) {
                    have = true;
                }
            }
            if (have) {
                result.add(snapshotLogEntity);
            }
        }

        return AjaxResult.success(result);
    }

}
