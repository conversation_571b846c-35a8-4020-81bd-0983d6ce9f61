package com.xhjt.project.ship.service.impl;

import com.xhjt.common.utils.SecurityUtils;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.ship.domain.CruiseStationEntity;
import com.xhjt.project.ship.mapper.CruiseStationMapper;
import com.xhjt.project.ship.service.CruiseStationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 航次站位管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class CruiseStationServiceImpl implements CruiseStationService {

    @Autowired
    private CruiseStationMapper cruiseStationMapper;

    /**
     * 查询航次信息
     *
     * @param id 航次ID
     * @return 航次信息
     */
    @Override
    public CruiseStationEntity selectCruiseStationById(Long id) {
        CruiseStationEntity cruiseStation = new CruiseStationEntity();
        cruiseStation.setId(id);
        return cruiseStationMapper.selectCruiseStation(cruiseStation);
    }

    /**
     * 查询航次列表
     *
     * @param cruiseStation 航次信息
     * @return 航次集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<CruiseStationEntity> selectCruiseStationListByDept(CruiseStationEntity cruiseStation) {
        return cruiseStationMapper.selectCruiseStationList(cruiseStation);
    }

    @Override
    public List<CruiseStationEntity> selectCruiseStationList(CruiseStationEntity cruiseStation) {
        return cruiseStationMapper.selectCruiseStationList(cruiseStation);
    }

    /**
     * 新增航次
     *
     * @param cruiseStation 航次信息
     * @return 结果
     */
    @Override
    public int addCruiseStation(CruiseStationEntity cruiseStation) {
        if (!SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getUserId())) {
            cruiseStation.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        return cruiseStationMapper.addCruiseStation(cruiseStation);
    }

    /**
     * 修改航次
     *
     * @param cruiseStation 航次信息
     * @return 结果
     */
    @Override
    public int updateCruiseStation(CruiseStationEntity cruiseStation) {
        if (!SecurityUtils.isAdmin(SecurityUtils.getLoginUser().getUser().getUserId())) {
            cruiseStation.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        }
        return cruiseStationMapper.updateCruiseStation(cruiseStation);
    }

    /**
     * 删除航次信息
     *
     * @param cruiseStationId 参数ID
     * @return 结果
     */
    @Override
    public int deleteCruiseStationById(Long cruiseStationId) {
        return cruiseStationMapper.deleteCruiseStationById(cruiseStationId);
    }

    /**
     * 批量删除参数信息
     *
     * @param cruiseStationIds 需要删除的参数ID
     * @return 结果
     */
    @Override
    public int deleteCruiseStationByIds(Long[] cruiseStationIds) {
        return cruiseStationMapper.deleteCruiseStationByIds(cruiseStationIds);
    }

}
