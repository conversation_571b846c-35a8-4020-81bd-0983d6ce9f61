package com.xhjt.project.api.domain;

/**
 * @description: sb21的scan数据 10秒一组
 * Example: <Sbe21Scan index='20938'><Field0>20939</Field0><Field1>29.96138</Field1></Sbe21Scan>
 * <Sbe21Scan index='(1)'><Field0>(2)</Field0><Field1>(3)</Field1><Field2>(4)</Field2><Field3>(5)</Field3><Field4>(6)</Field4><Field5>(7)</Field5><Field6>(8)</Field6></Sbe21Scan>
 * @author: rr
 * @create: 2020-06-10 09:39
 **/
public class Sbe21Vo {

    private String id;

    /**
     * SBE21温度
     */
    private String temperature;

    /**
     * 原位温度
     */
    private String situTemperature;

    /**
     * 盐度
     */
    private String salinity;

    /**
     * 溶解氧
     */
    private String dissolvedOxygen;

    /**
     * 叶绿素
     */
    private String chlorophyll;

    /**
     * 浊度
     */
    private String turbidity;

    /**
     * 录入时间
     */
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    private String initialBjTime;

    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 经度
     */
    private Double longitude;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public String getSituTemperature() {
        return situTemperature;
    }

    public void setSituTemperature(String situTemperature) {
        this.situTemperature = situTemperature;
    }

    public String getSalinity() {
        return salinity;
    }

    public void setSalinity(String salinity) {
        this.salinity = salinity;
    }

    public String getDissolvedOxygen() {
        return dissolvedOxygen;
    }

    public void setDissolvedOxygen(String dissolvedOxygen) {
        this.dissolvedOxygen = dissolvedOxygen;
    }

    public String getChlorophyll() {
        return chlorophyll;
    }

    public void setChlorophyll(String chlorophyll) {
        this.chlorophyll = chlorophyll;
    }

    public String getTurbidity() {
        return turbidity;
    }

    public void setTurbidity(String turbidity) {
        this.turbidity = turbidity;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }
}
