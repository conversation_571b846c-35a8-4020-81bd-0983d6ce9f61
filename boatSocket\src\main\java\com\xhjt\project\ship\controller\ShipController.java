package com.xhjt.project.ship.controller;

import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.service.ShipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 船只信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/ship")
public class ShipController extends BaseController {

    @Autowired
    private ShipService shipService;

    /**
     * 获取船只信息列表
     */
    @GetMapping("/list")
    public AjaxResult allList() {
        return AjaxResult.success(shipService.selectShipListByDeptId(new ShipEntity()));
    }

    /**
     * 根据参数编号获取详细信息
     */
    @GetMapping(value = "/{shipId}")
    public AjaxResult getInfo(@PathVariable Long shipId) {
        return AjaxResult.success(shipService.selectShipById(shipId));
    }

}
