* {
    margin: 0;
    padding: 0;
}

html {
    height: 100%;
}

body {
    height: 100%;
    color: white;
    position: relative;
    min-height: 700px;
    background: #C9D5E5;
}

.myBg {
    background: url("../../img/ship.png") no-repeat bottom;
    height: 100%;
    width: 100%;
    background-size: 100%;

}

.context {
    position: relative;
    padding-top: 300px;
    width: 500px;
    height: 400px;
    margin: 0 auto;
    text-align: center;

}

/*定位浏览器边距位置 左 上 */

.container { /*类选择器*/
    height: 400px;
    width: 500px;
    margin: 0 auto;
    top: 30px;

}

form {
    padding: 20px 0;
    position: relative;
}

input[type="text"], input[type="password"] {
    border: 1px solid #2D93CA;
    width: 358px;
    height: 53px;
    display: block;
    margin: 15px auto 10px auto;
    border-radius: 5px;
    font-size: 18px;
    font-weight: 300;
    text-align: center;
}

input[type="button"] {
    background: #40AFFE;
    border: 0;
    padding: 10px 15px;
    color: white;
    border-radius: 8px;
    width: 356px;
    height: 48px;
    font-size: 18px;
    margin: 10px auto;
    text-align: center;
    cursor: pointer;

}

.container #show {
    color: red;
    margin-top: 10px;
    font-size: 16px;
}
