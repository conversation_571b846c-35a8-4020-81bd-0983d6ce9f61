package com.xhjt.project.ship.mapper;


import com.xhjt.project.ship.domain.TravelActionNodeEntity;

import java.util.List;

/**
 * 行程动作节点 数据层
 *
 * <AUTHOR>
 */
public interface TravelActionNodeMapper {
    /**
     * 查询行程动作节点信息
     *
     * @param travelActionNode 行程动作节点信息
     * @return 行程动作节点信息
     */
    public TravelActionNodeEntity selectTravelActionNode(TravelActionNodeEntity travelActionNode);

    /**
     * 查询行程动作节点列表
     *
     * @param travelActionNode 行程动作节点信息
     * @return 行程动作节点集合
     */
    public List<TravelActionNodeEntity> selectTravelActionNodeList(TravelActionNodeEntity travelActionNode);

    /**
     * 新增行程动作节点
     *
     * @param travelActionNode 行程动作节点信息
     * @return 结果
     */
    public int addTravelActionNode(TravelActionNodeEntity travelActionNode);

    /**
     * 修改行程动作节点
     *
     * @param travelActionNode 行程动作节点信息
     * @return 结果
     */
    public int updateTravelActionNode(TravelActionNodeEntity travelActionNode);

    /**
     * 删除行程动作节点
     *
     * @param travelActionNodeId 参数ID
     * @return 结果
     */
    public int deleteTravelActionNodeById(Long travelActionNodeId);

    /**
     * 批量删除参数信息
     *
     * @param travelActionNodeIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteTravelActionNodeByIds(Long[] travelActionNodeIds);
}