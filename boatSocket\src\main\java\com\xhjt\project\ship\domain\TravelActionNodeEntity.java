package com.xhjt.project.ship.domain;

import com.xhjt.common.enums.TravelActionTypeEnum;
import com.xhjt.dctcore.commoncore.domain.BaseEntity;
import com.xhjt.dctcore.commoncore.utils.DateUtils;
import com.xhjt.framework.aspectj.lang.annotation.Excel;

/**
 * 行程动作节点信息
 *
 * <AUTHOR>
 */
public class TravelActionNodeEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Excel(name = "ID")
    private Long id;

    /**
     * 航次ID
     */
    @Excel(name = "航次ID")
    private Long cruiseId;

    @Excel(name = "航次编号")
    private String cruiseCode;

    /**
     * 关联的船只
     */
    @Excel(name = "船只sn")
    private String sn;

    @Excel(name = "船只名称")
    private String shipName;

    /**
     * 关联站位编号--可以为空
     */
    @Excel(name = "关联站位")
    private String relationStation;

    /**
     * 记录时间
     */
    private Long time;

    @Excel(name = "记录时间")
    private String bjTime;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private String longitude;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private String latitude;

    @Excel(name = "船速")
    private Double groundRate;

    /**
     * 主推4马达转速
     */
    @Excel(name = "主推4马达转速")
    private Double mt4MtrSpeed;

    /**
     * 主推5马达转速
     */
    @Excel(name = "主推5马达转速")
    private Double mt5MtrSpeed;

    /**
     * 和关联站位的距离
     */
    @Excel(name = "和关联站位的距离")
    private Double relationStationDistance;

    /**
     * 行为类型
     */
    private Integer actionType;

    /**
     * 行为类型
     */
    @Excel(name = "行为类型")
    private String actionTypeStr;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCruiseId() {
        return cruiseId;
    }

    public void setCruiseId(Long cruiseId) {
        this.cruiseId = cruiseId;
    }

    public String getCruiseCode() {
        return cruiseCode;
    }

    public void setCruiseCode(String cruiseCode) {
        this.cruiseCode = cruiseCode;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getRelationStation() {
        return relationStation;
    }

    public void setRelationStation(String relationStation) {
        this.relationStation = relationStation;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Double getGroundRate() {
        return groundRate;
    }

    public void setGroundRate(Double groundRate) {
        this.groundRate = groundRate;
    }

    public Double getMt4MtrSpeed() {
        return mt4MtrSpeed;
    }

    public void setMt4MtrSpeed(Double mt4MtrSpeed) {
        this.mt4MtrSpeed = mt4MtrSpeed;
    }

    public Double getMt5MtrSpeed() {
        return mt5MtrSpeed;
    }

    public void setMt5MtrSpeed(Double mt5MtrSpeed) {
        this.mt5MtrSpeed = mt5MtrSpeed;
    }

    public Double getRelationStationDistance() {
        return relationStationDistance;
    }

    public void setRelationStationDistance(Double relationStationDistance) {
        this.relationStationDistance = relationStationDistance;
    }

    public Integer getActionType() {
        return actionType;
    }

    public void setActionType(Integer actionType) {
        this.actionType = actionType;
    }

    public String getBjTime() {
        return DateUtils.getDateToString(this.time);
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getActionTypeStr() {
        for (TravelActionTypeEnum typeEnum : TravelActionTypeEnum.values()) {
            if (typeEnum.getValue().equals(this.actionType)) {
                return typeEnum.getAlias();
            }
        }
        return "";
    }

    public void setActionTypeStr(String actionTypeStr) {
        this.actionTypeStr = actionTypeStr;
    }
}
