package com.xhjt.project.api.service;

import com.xhjt.dctcore.commoncore.domain.hbase.RacduHbaseVo;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * racdu1 操作类
 */
@Service
@Transactional(readOnly = true)
public class Racdu1Service {

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    public RacduHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.RA_CDU1.getAlias(), deviceCode, 0);
        return hBaseDaoUtil.getLatestRow(new RacduHbaseVo(), tableName);
    }
}
