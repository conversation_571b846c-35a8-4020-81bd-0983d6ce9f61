package com.xhjt.project.ship.service.impl;

import com.xhjt.project.ship.domain.CruiseEntity;
import com.xhjt.project.ship.mapper.CruiseMapper;
import com.xhjt.project.ship.service.CruiseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 航次管理 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class CruiseServiceImpl implements CruiseService {

    @Autowired
    private CruiseMapper cruiseMapper;


    @Override
    public List<CruiseEntity> selectCruiseList(String sn, Long presentTime) {
        CruiseEntity cruise = new CruiseEntity();
        cruise.setSn(sn);
        cruise.setPresentTime(presentTime);
        return cruiseMapper.selectCruiseList(cruise);
    }

    /**
     * 查询航次列表
     *
     * @param cruise 航次信息
     * @return 航次集合
     */
    @Override
    public List<CruiseEntity> selectAllCruiseList(CruiseEntity cruise) {
        return cruiseMapper.selectAllCruiseList(cruise);
    }
}
