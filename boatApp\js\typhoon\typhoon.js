let selectYear = new Date().getFullYear();
let typhoonInfoMap = new Map();

let tyMarkerMap = new Map();
let tyLineMap = new Map();

$(function () {
    bind1();
});

function initTyphoon() {
    getTyphoonList(selectYear);
    queryTyphoonInfo();
}

function bind1() {
    //年选择器
    laydate.render({
        elem: '#timeYear',
        type: 'year',
        theme: '#09c',
        value: selectYear,
        trigger: 'click',
        change: function (value, date) {
            if (selectYear === date.year) {
                return;
            }
            selectYear = date.year;
            getTyphoonList(selectYear);
        }
    });

    $("#closeBox").click(function () {
        $("#tfBox").toggle(500);
        if ($(this).hasClass('active')) {
            $(this).animate({right: "+=285px"}, 500);
            $(this).removeClass("active");
        } else {
            $(this).animate({right: "-=285px"}, 500);
            $(this).addClass("active");
        }
    });
}

//年份的所有台风
function getTyphoonList(year) {
    if (year === undefined) {
        year = new Date().getFullYear();
    }
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/typhoon/getTyphoonData",
        data: {yearsTime: year},
        dataType: "json",
        async: false,
        success: function (result) {
            let data = result.data;
            let $typhoonListDiv = $('#typhoonListDiv');
            cleanAllTyphoon();

            let html = '';
            html += '<select style="width: 67px;height: 21px;" id="typhoonSelect" class="typhoonSelect" multiple="multiple">';

            if (!isEmpty(data)) {
                for (let i = 0; i < data.length; i++) {
                    if ((new Date().getTime() - data[i].endTimeStamp) < 24 * 60 * 60 * 1000) {
                        html += '<option value=' + data[i].tfid + ' selected>' + data[i].tfid + data[i].name + '</option>';
                    } else {
                        html += '<option value=' + data[i].tfid + '>' + data[i].tfid + data[i].name + '</option>';
                    }
                }
            }
            html += '</select>';
            $typhoonListDiv.append(html);
            //复选框select
            $('.typhoonSelect').fSelect();
        }
    });
}

// 点击获取
function queryTyphoonInfo() {
    let idList = [].map.call($('.fs-option.selected'),
        function (el) {
            return el.dataset.value
        });
    if (idList.length > 4) {
        alert("最多可选四个");
    }
    cleanMapDataList();
    typhoonInfoMap.clear();
    idList.forEach(tfId => {
        typhoonInfo(Number(tfId));
    });
    // 展示台风名称
    showPointName();
    let pointCoord = [];
    let switchTfId;
    let haveActive = false;
    typhoonInfoMap.forEach(function (v, k) {
        let tfId = Number(v.tfid);
        pointCoord = getPointCoordinate(tfId, v.name, v.points);

        let tfPolyline = BM.polyline(pointCoord, {color: "#588AF6",}).addTo(map);
        addLine2Map(tfId, tfPolyline);
        if (v.isactive === "1") {
            haveActive =  true;
        }
        switchTfId = haveActive || switchTfId === undefined ? tfId : switchTfId;
    });
    if(switchTfId !== undefined){
        typhoonSwitch(switchTfId);
    } else {
        showPointList();
    }

    pointCoord = [];
}

// 获取单个台风信息，包括点位、预测等
function typhoonInfo(tfId) {
    //获取单个台风的所有信息
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/typhoon/getTyphoonPoint",
        data: {tfId: tfId},
        dataType: "json",
        async: false,
        contentType: 'application/json;charset=utf-8',
        success: function (result) {
            let data = result.data;
            typhoonInfoMap.set(tfId, data[0]);
        }
    });
}

// 点位列表展示
function showPointList(sourceList, tfId) {
    let tableContent = "";
    if (isEmpty(sourceList)) {
        tableContent += '<img src="img/typhoon/noyt.png" style="max-width: 273px;margin-left: 2px;">';
    } else {
        //进行降序输出
        let pointList = Object.create(sourceList);
        pointList.sort(function (a, b) {
            return new Date(a.time) < new Date(b.time) ? 1 : -1
        });
        for (let i = 0; i < pointList.length; i++) {
            let dateTime = new Date(pointList[i].time).getTime();
            //tr 需要唯一标识  点击后颜色改变： background-color: aqua;  id 去除空格
            tableContent += '<tr id=\'' + tfId + '' + dateTime + '\'  onmousedown ="pointTableMouseDown(' + dateTime + ',' + tfId + ')"' +
                ' style="cursor:pointer">' +
                '<td class="td35 tdnormal">' + timeFormat(pointList[i].time) + '</td>' +
                '<td class="td25 tdnormal">' + pointList[i].pressure + '</td>' +
                '<td class="td25 tdnormal">' + pointList[i].power + '</td>' +
                '<td class="td25 tdnormal">' + pointList[i].movespeed + '</td>' +
                '</tr>';
        }
    }
    $("#typhoonPointList").html(tableContent);
}

//台风名拼接
function showPointName() {
    let html = "";
    typhoonInfoMap.forEach(function (v, k) {
        if (isEmpty(v) || isEmpty(v.points)) {
            html += '<li class="namelist-select" style="width: 279px; text-align: left; line-height: 43px; font-weight: bold;">\n' +
                '<img src="img/typhoon/announce.gif" style="vertical-align: -3px;" />&nbsp;&nbsp;<span id="nametab">当前西太平洋无台风</span>\n' +
                '</li>';
        } else {
            if (v.isactive === "0") {
                html += '<li id=\'' + k + '\' >' +
                    '<a onclick="closePointTable(' + k + ')"><img src="img/typhoon/tfbox/closen.png"  ></a>' +
                    '<i style="background:#0099CC;border-radius: 50%;" onclick="typhoonSwitch(' + k + ')">' +
                    '<br><span style="position: absolute; margin-top: -4px;margin-left: -48px;">' + v.name + '</span><br>' +
                    '<span style="position: absolute; margin-top: -4px;margin-left: -56px;">' + v.points[v.points.length - 1].strong + '</span>' +
                    '</i>' +
                    '</li>';
            } else {
                html += '<li id=\'' + k + '\'  onclick="typhoonSwitch(' + k + ')">' +
                    '<i style="background:#0099CC;border-radius: 50%;">' +
                    '<img src="img/typhoon/status/refresh.gif" style="width: 14px;" ><br>' +
                    '<span style="position: absolute; margin-top: -4px;margin-left: -48px;">' + v.name + '</span><br>' +
                    '<span style="position: absolute; margin-top: -4px;margin-left: -56px;">' + v.points[v.points.length - 1].strong + '</span>' +
                    '</i>' +
                    '</li>';
            }
        }
    });
    $("#nameList").html(html);
}

// 点击切换显示台风列表
function typhoonSwitch(tfId) {
    let $tyName = $('#' + tfId);
    if ($tyName !== undefined && $tyName.hasClass('tyNameSelect')) {
        return;
    }

    let pointList = typhoonInfoMap.get(tfId).points;
    showPointList(pointList, tfId);

    $('#nameList').children().each(function (index, e) {
        if ($(e).hasClass('tyNameSelect')) {
            $(e).removeClass('tyNameSelect');
        }
    });
    $tyName.addClass('tyNameSelect');
}

// 点击关闭台风列表，同时清除该台风轨迹、点位
function closePointTable(tfId) {
    cleanMapDataList(tfId);
    typhoonInfoMap.delete(tfId);
    // 查看已有得选择是哪个台风，若是被关闭的台风则选下一次展示
    let switchId = tfId;
    $('#nameList').children().each(function (index, e) {
        if ($(e).hasClass('tyNameSelect')) {
            switchId = Number($(e).attr('id'));
        }
    });
    showPointName();

    if (typhoonInfoMap.size === 0) {
        showPointList();
        return;
    }

    if (switchId === tfId) {
        switchId = typhoonInfoMap.keys().next().value;
    }
    typhoonSwitch(switchId);
}

// 获取台风点位的坐标数组
function getPointCoordinate(tfId, tyName, allTyPointList, endTime) {
    //定义折线点数据的新数组
    let polylinePoints = [];
    if (isEmpty(allTyPointList)) {
        return polylinePoints;
    }
    let tyPointList = [];
    for (let i = 0; i < allTyPointList.length; i++) {
        let thisTime = new Date(allTyPointList[i].time).getTime();
        if (endTime !== undefined && thisTime > endTime) {
            continue;
        }
        tyPointList.push(allTyPointList[i]);
    }

    let gpsArr;
    //找到经纬度数据，存放在新数组中
    for (let i = 0; i < tyPointList.length; i++) {
        gpsArr = [];
        gpsArr.push(Number(tyPointList[i]['lat']));
        gpsArr.push(Number(tyPointList[i]['lng']));
        polylinePoints.push(gpsArr);

        if (i === 0) {
            drawCircleMarker(tfId, tyName, tyPointList[i], gpsArr, 0);
        } else if (i === tyPointList.length - 1) {
            drawGifMarker(tfId, tyName, tyPointList[i], gpsArr);
            sector(tfId, gpsArr, tyPointList[i].radius7, "七级", "0.5");
            sector(tfId, gpsArr, tyPointList[i].radius10, "十级", "0.7");
            sector(tfId, gpsArr, tyPointList[i].radius12, "十二级", "1");
            drawForecast(tfId, tyPointList[i].forecast);
        } else {
            drawCircleMarker(tfId, tyName, tyPointList[i], gpsArr, 1);
        }
    }
    tyPointList = undefined;
    return polylinePoints;
}

// 画台风最终点标记(动态旋转台风图像)
function drawGifMarker(tfId, tyName, tyPoint, gpsArr) {
    if (gpsArr === []) {
        return;
    }
    let html = getMarkerHtml(tyName, tyPoint);

    // //实时台风头图标
    let typhoonIcon = BM.marker(gpsArr, {
        icon: BM.icon({
            iconUrl: 'img/typhoon/typhoon.gif',
            iconSize: [40, 40],
            iconAnchor: [15, 18],
            popupAnchor: [-3, -76],
        }),
    }).addTo(map);
    typhoonIcon.bindTooltip(html).openTooltip();

    addMarker2Map(tfId, typhoonIcon);
}

//画点
function drawCircleMarker(tfId, tyName, pointInfo, gpsArr, type) {
    let html = getMarkerHtml(tyName, pointInfo);
    let color = getColor(pointInfo.strong);
    let marker;
    if (type === 0) {
        marker = BM.circleMarker(gpsArr, {
            radius: 3,
            color: color,
            weight: 3,
            fill: true,
            fillColor: color,
            fillOpacity: 1,
        }).bindTooltip(tyName, {permanent: true, offset: [10, 0], direction: 'right'}).addTo(map);
    } else {
        marker = BM.circleMarker(gpsArr, {
            radius: 3,
            color: color,
            weight: 3,
            fill: true,
            fillColor: color,
            fillOpacity: 1,
        }).bindTooltip(html).openTooltip().addTo(map);
    }
    addMarker2Map(tfId, marker);
}

// 台风点位显示
function getMarkerHtml(tyName, pointInfo) {
    return "<div class='popuoCss'><B>" + tyName + "</B> " + " 时间：" + pointInfo.time + "\<br\>" +
        "<B>中心位置：</B>" + pointInfo.lng + "°/" + pointInfo.lat + "°\<br\>" +
        "<B>风速风力：</B>" + pointInfo.speed + "米/每秒," + pointInfo.strong + "\<br\>" +
        "<B>中心气压：</B>" + pointInfo.pressure + "百帕" + "\<br\>" +
        '<B>移速移向：</B>' + pointInfo.movespeed + '公里/小时,' + pointInfo.movedirection + '\<br\>';
}

//最后一个点的四个扇形区域
function sector(tfId, gpsArr, tyPoint, level, opacity) {
    if (isEmpty(tyPoint)) {
        return;
    }
    let radiusAll = tyPoint.split("|");
    let ss = radiusAll[2];
    radiusAll.splice(2, 1);
    radiusAll.push(ss);
    //扇形的半径
    let basis = 800;
    //扇形图（用于清除）
    let marker;
    for (let i = 0; i < 4; i++) {
        let radius = radiusAll[i];
        // //添加开始的位置
        let path = [gpsArr];
        let html = "<div class='popuoCss'><B>" + level + "风圈</B> " + "\<br\>" +
            "<B>西北：</B>" + tyPoint.split("|")[2] + "km | 东北：" + tyPoint.split("|")[0] + "km\<br\>" +
            "<B>西南：</B>" + tyPoint.split("|")[3] + "km | 东南：" + tyPoint.split("|")[1] + "km\<br\>";
        for (let j = 0; j < 362; j++) {
            //依次计算距离中心点 5000米偏移角度为 i*90+j/4-20 的点
            path.push(BM.GeometryUtil.destination({
                lat: Number(gpsArr[0]),
                lng: Number(gpsArr[1])
            }, i * 90 + j / 4 - 2, radius * basis));
        }
        marker = BM.polygon(path, {
            fillColor: "#FDD49F",
            fillOpacity: opacity,
            color: "#FDD49F",
            weight: 1,
            opacity: 0.1
        }).addTo(map);
        marker.bindTooltip(html).addTo(map);

        addMarker2Map(tfId, marker);
    }
}

//预测轨迹
function drawForecast(tfId, forecastList) {
    let countryData = {
        china: [], //中国
        taiwan: [], //中国台湾
        japan: [], //日本
        hongKong: [], //中国香港
        usa: [], //美国
    };
    let foldLineYColor = ['#FF3C4E', '#FF00FE', '#458B00', '#FEBD00', '#04FAF7'];
    for (let i = 0; i < forecastList.length; i++) {
        switch (forecastList[i].tm) {
            case "中国":
                countryData.china = eval(forecastList[i].forecastPoints);
                break;
            case "中国台湾":
                countryData.taiwan = eval(forecastList[i].forecastPoints);
                break;
            case "日本":
                countryData.japan = eval(forecastList[i].forecastPoints);
                break;
            case "中国香港":
                countryData.hongKong = eval(forecastList[i].forecastPoints);
                break;
            case "美国":
                countryData.usa = eval(forecastList[i].forecastPoints);
                break;
        }
    }
    let china = BM.polyline(countryData.china, {color: foldLineYColor[0], dashArray: [8, 5], weight: 1,}).addTo(map);
    let taiwan = BM.polyline(countryData.taiwan, {color: foldLineYColor[1], dashArray: [8, 5], weight: 1}).addTo(map);
    let japan = BM.polyline(countryData.japan, {color: foldLineYColor[2], dashArray: [8, 5], weight: 1.2}).addTo(map);
    let hk = BM.polyline(countryData.hongKong, {color: foldLineYColor[3], dashArray: [8, 5], weight: 1}).addTo(map);
    let usa = BM.polyline(countryData.usa, {color: foldLineYColor[4], dashArray: [8, 5], weight: 1}).addTo(map);
    addLine2Map(tfId, china);
    addLine2Map(tfId, taiwan);
    addLine2Map(tfId, japan);
    addLine2Map(tfId, hk);
    addLine2Map(tfId, usa);

    drawForecastMark(tfId, forecastList);
}

//预测轨迹点
function drawForecastMark(tfId, forecastList) {
    for (let i = 0; i < forecastList.length; i++) {
        for (let j = 0; j < forecastList[i].forecastPoints.length; j++) {
            let color = getColor(forecastList[i].forecastPoints[j].strong);
            let html = getForecastMarkerHtml(forecastList[i].tm, forecastList[i].forecastPoints[j]);

            let marker = BM.circleMarker([Number(forecastList[i].forecastPoints[j]['lat']), Number(forecastList[i].forecastPoints[j]['lng'])],
                {
                    radius: 3,
                    color: color,
                    weight: 3,
                    fill: true,
                    fillColor: color,
                    fillOpacity: 1,
                }).bindTooltip(html).openTooltip().addTo(map);

            addMarker2Map(tfId, marker);
        }
    }
}

// 台风点位显示
function getForecastMarkerHtml(tm, forecastPoint) {
    return "<div class='popuoCss'><B>" + tm + "</B> " + " 时间：" + forecastPoint.time + " 预报\<br\>" +
        "<B>当前位置：</B>" + forecastPoint.lng + "°/" + forecastPoint.lat + "°\<br\>" +
        "<B>最大风速：</B>" + forecastPoint.speed + "米/每秒" + "\<br\>" +
        "<B>中心气压：</B>" + forecastPoint.pressure + "百帕" + "\<br\>" +
        '<B>风力：</B>' + forecastPoint.power + '级以上 \<br\>';
}

// 点击台风点位列表，显示台风移至该点，且显示该点的预测
function pointTableMouseDown(endTime, tfId) {
    cleanMapDataList(tfId);

    let pointCoord = [];
    typhoonInfoMap.forEach(function (v, k) {
        if (Number(v.tfid) === tfId) {
            pointCoord = getPointCoordinate(tfId, v.name, v.points, endTime);
            let tfPolyline = BM.polyline(pointCoord, {color: "#588AF6",}).addTo(map);
            addLine2Map(tfId, tfPolyline);
        }
    });
    pointCoord = [];

    $('#typhoonPointList').children().each(function (index, e) {
        if ($(e).hasClass('pointSelectBg')) {
            $(e).removeClass('pointSelectBg');
        }
    });
    $('#' + tfId + endTime).addClass('pointSelectBg');
}

//时间格式化
function timeFormat(time) {
    if (!time) return "";
    let date = new Date(time);
    // let yyyy = date.getFullYear();
    let mm = date.getMonth() + 1;
    let dd = date.getDate();
    let hours = date.getHours()
    return `${mm}月${dd}日 ${hours}时`;
}

// 清除地图上的点、线
function cleanMapDataList(tfId) {
    tyMarkerMap.forEach(function (v, key) {
        if (tfId === undefined || key === tfId) {
            let tyMarkerList = tyMarkerMap.get(key);
            tyMarkerList.forEach(function (marker) {
                marker.remove();
            });
            tyMarkerMap.delete(key);
        }
    });

    tyLineMap.forEach(function (v, key) {
        if (tfId === undefined || key === tfId) {
            let tyLineList = tyLineMap.get(key);
            tyLineList.forEach(function (line) {
                line.remove();
            });
            tyLineMap.delete(key);
        }
    });
}

//切换年份的时候清空台风信息
function cleanAllTyphoon() {
    let $nameList = $('#nameList');
    $nameList.empty();

    let $typhoonListDiv = $('#typhoonListDiv');
    $typhoonListDiv.empty();

    showPointList();

    cleanMapDataList();

}

// 按id添加地图上的点
function addMarker2Map(tfId, marker) {
    let tyMarkerList = tyMarkerMap.get(tfId);
    if (isEmpty(tyMarkerList)) {
        tyMarkerList = [];
    }
    tyMarkerList.push(marker);
    tyMarkerMap.set(tfId, tyMarkerList);
}

// 按id添加地图上的线
function addLine2Map(tfId, line) {
    let tyLineList = tyLineMap.get(tfId);
    if (isEmpty(tyLineList)) {
        tyLineList = [];
    }
    tyLineList.push(line);
    tyLineMap.set(tfId, tyLineList);
}

//获取颜色 strong:气压名
function getColor(strong) {
    let color;
    switch (strong) {
        case "热带低压":
            color = "#02FF02";
            break;
        case "热带风暴":
            color = "#0264FF";
            break;
        case "强热带风暴":
            color = "#FFFB05";
            break;
        case "台风":
            color = "#FFAC05";
            break;
        case "强台风":
            color = "#F171F9";
            break;
        case "超强台风":
            color = "#FE0202";
            break;
    }
    return color;
}
