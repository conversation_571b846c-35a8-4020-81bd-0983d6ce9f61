package com.xhjt.project.ship.mapper;

import com.xhjt.project.ship.domain.CruiseStationEntity;

import java.util.List;

/**
 * 航次站位管理 数据层
 *
 * <AUTHOR>
 */
public interface CruiseStationMapper {
    /**
     * 查询航次信息
     *
     * @param cruiseStation 航次信息
     * @return 航次信息
     */
    public CruiseStationEntity selectCruiseStation(CruiseStationEntity cruiseStation);

    /**
     * 查询航次列表
     *
     * @param cruiseStation 航次信息
     * @return 航次集合
     */
    public List<CruiseStationEntity> selectCruiseStationList(CruiseStationEntity cruiseStation);

    /**
     * 新增航次
     *
     * @param cruiseStation 航次信息
     * @return 结果
     */
    public int addCruiseStation(CruiseStationEntity cruiseStation);

    /**
     * 修改航次
     *
     * @param cruiseStation 航次信息
     * @return 结果
     */
    public int updateCruiseStation(CruiseStationEntity cruiseStation);

    /**
     * 删除航次
     *
     * @param cruiseStationId 参数ID
     * @return 结果
     */
    public int deleteCruiseStationById(Long cruiseStationId);

    /**
     * 批量删除参数信息
     *
     * @param cruiseStationIds 需要删除的参数ID
     * @return 结果
     */
    public int deleteCruiseStationByIds(Long[] cruiseStationIds);
}