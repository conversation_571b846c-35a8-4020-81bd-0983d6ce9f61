package com.xhjt.project.api.controller;

import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.api.service.*;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.DeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/api/device/data")
public class DeviceDataController extends BaseController {

    @Autowired
    private DeviceService deviceService;
    @Autowired
    private AwsService awsService;
    @Autowired
    private Co2Service co2Service;
    @Autowired
    private CompassService compassService;
    @Autowired
    private Ea600Service ea600Service;
    @Autowired
    private GpsService gpsService;
    @Autowired
    private LogService logService;
    @Autowired
    private Sbe21Service sbe21Service;
    @Autowired
    private WindService windService;

    @Autowired
    private RealtimeService realtimeService;

    @Autowired
    private GpsInService gpsInService;

    @Autowired
    private CompassInService compassInService;

    /**
     * 获取数据
     */
    @GetMapping("/list")
    public AjaxResult list(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {
        if (StringUtils.isBlank(sn) || StringUtils.isBlank(deviceCode)) {
            return AjaxResult.error("获取失败，缺少参数");
        }

        DeviceEntity device = deviceService.getDeviceBySnAndCode(sn, deviceCode);
        if (device == null) {
            return AjaxResult.error("获取失败，没有该设备");
        }

        if (DeviceTypeEnum.AWS.getValue().equals(device.getType())) {
            return AjaxResult.success(awsService.queryByTime(sn, deviceCode, interval, startTime, endTime));
        }
        if (DeviceTypeEnum.GO8050.getValue().equals(device.getType())) {
            return AjaxResult.success(co2Service.queryByTime(sn, deviceCode, interval, startTime, endTime));
        }
        if (DeviceTypeEnum.COMPASS.getValue().equals(device.getType())) {
            return AjaxResult.success(compassService.queryByTime(sn, deviceCode, interval, startTime, endTime));
        }
        if (DeviceTypeEnum.EA600.getValue().equals(device.getType())) {
            return AjaxResult.success(ea600Service.queryByTime(sn, deviceCode, interval, startTime, endTime));
        }
        if (DeviceTypeEnum.GPS.getValue().equals(device.getType())) {
            return AjaxResult.success(gpsService.getGpsData(sn, deviceCode, interval, startTime, endTime));
        }
        if (DeviceTypeEnum.LOG.getValue().equals(device.getType())) {
            return AjaxResult.success(logService.queryByTime(sn, deviceCode, interval, startTime, endTime));
        }
        if (DeviceTypeEnum.SBE21.getValue().equals(device.getType())) {
            return AjaxResult.success(sbe21Service.queryByTime(sn, deviceCode, interval, startTime, endTime));
        }
        if (DeviceTypeEnum.WIND.getValue().equals(device.getType())) {
            return AjaxResult.success(windService.queryByTime(sn, deviceCode, interval, startTime, endTime));
        }
        if (DeviceTypeEnum.GPS_IN.getValue().equals(device.getType())) {
            return AjaxResult.success(gpsInService.queryByTime(sn, deviceCode, interval, startTime, endTime));
        }
        if (DeviceTypeEnum.COMPASS_IN.getValue().equals(device.getType())) {
            return AjaxResult.success(compassInService.queryByTime(sn, deviceCode, interval, startTime, endTime));
        }
        return AjaxResult.error("获取失败，没有该类型设备");
    }


    @GetMapping("/getLatestData")
    public AjaxResult getLatestData(String sn, String deviceCode) {
        return AjaxResult.success(realtimeService.getLatestData(sn, deviceCode));
    }
}
