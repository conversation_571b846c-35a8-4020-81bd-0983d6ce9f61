let allRouters = [];

function loadMenu(name) {
    $.ajax({
        type: "GET",
        url: laputaHost + "/getRouters",
        dataType: 'json',
        success: function (result) {
            if (result.code !== 200) {
                return;
            }
            //  没有任何权限则需要退到登录页面
            if (result.data === undefined || result.data === [] || result.data === null || result.data === '') {
                window.location.href = "login.html";
            }
            if (!judgeAuth(name, result.data)) {
                window.location.href = result.data[0].path;
            }
            allRouters = result.data;
            buildMenu(allRouters);
        }
    });
}

/**
 * 判断是否有访问页面的权限，无则跳转到有权限的第一个页面
 * @param path 访问的页面路径
 * @param routers 所有菜单权限
 */
function judgeAuth(path, routers) {
    for (let i = 0; i < routers.length; i++) {
        if (routers[i].path.indexOf(path) !== -1) {
            return true;
        }
        if (routers[i].children === undefined) {
            continue;
        }
        if (judgeAuth(path, routers[i].children)) {
            return true;
        }
    }

    return false;
}

/**
 * 构建菜单
 * @param routers
 */
function buildMenu(routers) {
    let htmlStr = '';
    for (let i = 0; i < routers.length; i++) {
        if (routers[i].path.indexOf('index.html') !== -1) {
            continue;
        }
        htmlStr += '<a href="' + routers[i].path + '" onmouseover="buildMenu2(' + i + ')">' + routers[i].meta.title + '</a>';
    }
    $(".submenu")[0].innerHTML = htmlStr;
}

function buildMenu2(i) {
    let htmlStr = '';

    if (allRouters[i].children !== undefined && allRouters[i].children !== null && allRouters[i].children !== '') {
        for (let j = 0; j < allRouters[i].children.length; j++) {
            htmlStr += '<a href="' + allRouters[i].children[j].path + '">' + allRouters[i].children[j].meta.title + '</a>';
        }
    }

    $(".submenu2")[0].innerHTML = htmlStr;
}

