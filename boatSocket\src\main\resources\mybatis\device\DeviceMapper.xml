<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xhjt.project.device.mapper.DeviceMapper">

    <resultMap type="DeviceEntity" id="DeviceResult">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="sn" column="sn"/>
        <result property="shipName" column="ship_name"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="code" column="code"/>
        <result property="enable" column="enable"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectDeviceVo">
        select de.id, de.dept_id, de.sn,s.name ship_name, de.name, de.type, de.code,de.enable, de.remark, de.create_by, de.create_time, de.update_by, de.update_time
		from device de
	      left join ship s on de.sn = s.sn
	      left join sys_dept d on de.dept_id = d.dept_id
    </sql>

    <select id="selectDevice" parameterType="DeviceEntity" resultMap="DeviceResult">
        <include refid="selectDeviceVo"/>
        <where>
            <if test="id !=null">
                and de.id = #{id}
            </if>
            <if test="deptId != null and deptId != 0">
                AND (de.dept_id = #{deptId} OR de.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="sn !=null and sn != ''">
                and de.sn = #{sn}
            </if>
            <if test="name !=null and name != ''">
                and de.name = #{name}
            </if>
            <if test="code !=null and code != ''">
                and de.code = #{code}
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
    </select>

    <select id="selectDeviceList" parameterType="DeviceEntity" resultMap="DeviceResult">
        <include refid="selectDeviceVo"/>
        <where>
            <if test="deptId != null and deptId != 0">
                AND (d.dept_id = #{deptId} OR d.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{deptId},ancestors) ))
            </if>
            <if test="name != null and name != ''">
                AND de.name like concat('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND de.code like concat('%', #{code}, '%')
            </if>
            <if test="sn !=null and sn != ''">
                AND de.sn = #{sn}
            </if>
            <if test="type !=null ">
                AND de.type = #{type}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                and date_format(de.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                and date_format(de.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <!-- 数据范围过滤 -->
            ${dataScope}
        </where>
    </select>


</mapper>