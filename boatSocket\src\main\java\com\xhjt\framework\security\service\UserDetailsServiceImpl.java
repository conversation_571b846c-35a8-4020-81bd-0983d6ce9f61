package com.xhjt.framework.security.service;

import com.xhjt.common.enums.UserStatus;
import com.xhjt.common.exception.BaseException;
import com.xhjt.common.utils.ServletUtils;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.common.utils.ip.IpUtils;
import com.xhjt.dctcore.commoncore.utils.IpUtil;
import com.xhjt.framework.security.LoginUser;
import com.xhjt.project.system.domain.SysUser;
import com.xhjt.project.system.service.ISysUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户验证处理
 *
 * <AUTHOR>
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    private static final Logger log = LoggerFactory.getLogger(UserDetailsServiceImpl.class);

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysPermissionService permissionService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        SysUser user = userService.selectUserByUserName(username);
        if (StringUtils.isNull(user)) {
            log.info("登录用户：{} 不存在.", username);
            throw new UsernameNotFoundException("登录用户：" + username + " 不存在");
        } else if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            log.info("登录用户：{} 已被删除.", username);
            throw new BaseException("对不起，您的账号：" + username + " 已被删除");
        } else if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            log.info("登录用户：{} 已被停用.", username);
            throw new BaseException("对不起，您的账号：" + username + " 已停用");
        }

        if(user.getAuthentication().equals("2")){
            String ipAddress = IpUtils.getIpAddr(ServletUtils.getRequest());
            //双重认证
            String bindingIp = user.getBindingIp();
            List<String> ipList = IpUtils.getIpList(bindingIp);
            if(!ipList.contains(ipAddress)){
                throw new InternalAuthenticationServiceException("IP不符合条件");
            }
        }

        return createLoginUser(user);
    }

    public UserDetails createLoginUser(SysUser user) {
        return new LoginUser(user, permissionService.getMenuPermission(user));
    }
}
