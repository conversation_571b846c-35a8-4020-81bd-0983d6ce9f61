package com.xhjt.project.api.service;

import com.xhjt.dctcore.commoncore.domain.hbase.CompassHbaseVo;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * compass 操作类
 */
@Service
@Transactional(readOnly = true)
public class CompassService {

    private Logger logger = LoggerFactory.getLogger(CompassService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public List<CompassHbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.COMPASS.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);

        return hBaseDaoUtil.scanByRowList(new CompassHbaseVo(), tableName, rowList);
    }


    public CompassHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.COMPASS.getAlias(), deviceCode, 0);
        return hBaseDaoUtil.getLatestRow(new CompassHbaseVo(), tableName);
    }
}
