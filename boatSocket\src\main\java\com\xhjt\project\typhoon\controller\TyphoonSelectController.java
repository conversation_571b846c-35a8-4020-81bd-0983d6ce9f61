package com.xhjt.project.typhoon.controller;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.framework.feign.TyphoonApi;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.typhoon.domain.Typhoon;
import com.xhjt.project.typhoon.domain.TyphoonInfoVo;
import com.xhjt.project.typhoon.service.SelectTyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 查台风数据
 * @author: rr
 * @create: 2020-10-27 17:36
 **/
@CrossOrigin
@RestController
@RequestMapping("/api/typhoon")
public class TyphoonSelectController extends BaseController {
    @Autowired
    private SelectTyService selectTyService;

    @Autowired
    private TyphoonApi typhoonApi;

    @GetMapping("/syncHis")
    public void syncHis(Integer year) {
        typhoonApi.syncHis(year);
    }
    @GetMapping("/getTyphoonData")
    public AjaxResult getTyphoonData(Integer yearsTime) {
        String list= typhoonApi.selectTyphoonList(yearsTime);
        List<Typhoon> typhoonList = JSONObject.parseArray(list, Typhoon.class);
        return AjaxResult.success(typhoonList);

    }

    @GetMapping("/getTyphoonPoint")
    public AjaxResult getTyphoonPoint(Integer tfId) {
        String list= typhoonApi.selectTyphoonPoint(tfId);
        List<TyphoonInfoVo> typhoonInfoVoList = JSONObject.parseArray(list, TyphoonInfoVo.class);
        return AjaxResult.success(typhoonInfoVoList);
    }

}
