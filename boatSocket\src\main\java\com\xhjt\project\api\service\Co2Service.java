package com.xhjt.project.api.service;

import com.xhjt.dctcore.commoncore.domain.hbase.Co2HbaseVo;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * co2 操作类
 */
@Service
@Transactional(readOnly = true)
public class Co2Service {

    private Logger logger = LoggerFactory.getLogger(Co2Service.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public List<Co2HbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.GO8050.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);

        return hBaseDaoUtil.scanByRowList(new Co2HbaseVo(), tableName, rowList);
    }


    public Co2HbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.GO8050.getAlias(), deviceCode, 0);
        return hBaseDaoUtil.getLatestRow(new Co2HbaseVo(), tableName);
    }

}
