package com.xhjt.common.utils;

import com.google.common.base.Function;
import com.google.common.collect.Maps;
import com.xhjt.common.exception.BaseException;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.IllegalClassException;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
 * <b>功能描述:</b>
 * <pre>
 * map操作工具
 */
public class MapUtil {


    /**
     * 对象转map
     *
     * @param t
     * @param <T>
     * @return
     */
    public static <T> Map<String, Object> object2Map(T t) {
        Map map = null;
        try {
            map = BeanUtils.describe(t);
        } catch (Exception e) {
            throw new BaseException("对象转Map出错！");
        }

        return map;
    }

    /**
     * 将Bean集合转换为Map形式：key为Long型，value为bean对象
     *
     * @param list         需要转换的集合
     * @param keyFieldName 作为key的字段名称
     * @param <T>          对象类型
     * @return
     */
    public static <T> Map<String, T> transBean2StringKeyMap(List<T> list, String keyFieldName)
            throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        Map<String, T> dbPMap = new HashMap<String, T>();

        if (CollectionUtils.isEmpty(list)) {
            return dbPMap;
        }

        String keyValue = null;
        for (T obj : list) {
            keyValue = BeanUtils.getProperty(obj, keyFieldName);

            if (StringUtils.isBlank(keyValue)) {
                continue;
            }

            dbPMap.put(String.valueOf(keyValue), obj);
        }

        return dbPMap;
    }

    /**
     * 将Bean集合转换为Map形式：key为Long型，value为bean对象
     *
     * @param list         需要转换的集合
     * @param keyFieldName 作为key的字段名称
     * @param <T>          对象类型
     * @return
     */
    public static <T> Map<Long, T> transBean2LongKeyMap(List<T> list, String keyFieldName)
            throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        Map<Long, T> dbPMap = new HashMap<Long, T>();

        if (CollectionUtils.isEmpty(list)) {
            return dbPMap;
        }

        String keyValue = null;
        for (T obj : list) {
            keyValue = BeanUtils.getProperty(obj, keyFieldName);

            if (StringUtils.isBlank(keyValue)) {
                continue;
            }

            dbPMap.put(Long.valueOf(keyValue), obj);
        }

        return dbPMap;
    }

    /**
     * 将Bean集合转换为Map形式：key为Long型，value为指定字段值Long值
     *
     * @param list         需要转换的集合
     * @param keyFieldName 作为key的字段名称
     * @param <T>          对象类型
     * @return
     */
    public static <T> Map<Long, Long> transBean2LongMap(List<T> list, String keyFieldName, String valueFieldName)
            throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        Map<Long, Long> dbPMap = new HashMap<Long, Long>();

        if (CollectionUtils.isEmpty(list)) {
            return dbPMap;
        }

        String key = null;
        String value = null;
        for (T obj : list) {
            key = BeanUtils.getProperty(obj, keyFieldName);
            value = BeanUtils.getProperty(obj, valueFieldName);
            if (StringUtils.isBlank(key)) {
                continue;
            }

            if (StringUtils.isBlank(value)) {
                value = "0";
            }

            dbPMap.put(Long.valueOf(key), Long.valueOf(value));
        }

        return dbPMap;
    }

    /**
     * 将Bean集合转换为Map形式：key为Long型，value为指定字段值String值
     *
     * @param list         需要转换的集合
     * @param keyFieldName 作为key的字段名称
     * @param <T>          对象类型
     * @return
     */
    public static <T> Map<Long, String> transBean2StringMap(List<T> list, String keyFieldName, String valueFieldName)
            throws IllegalAccessException, NoSuchMethodException, InvocationTargetException {
        Map<Long, String> dbPMap = new HashMap<Long, String>();

        if (CollectionUtils.isEmpty(list)) {
            return dbPMap;
        }

        String key = null;
        String value = null;
        for (T obj : list) {
            key = BeanUtils.getProperty(obj, keyFieldName);
            value = BeanUtils.getProperty(obj, valueFieldName);
            if (StringUtils.isBlank(key)) {
                continue;
            }

            if (StringUtils.isBlank(value)) {
                value = "0";
            }

            dbPMap.put(Long.valueOf(key), String.valueOf(value));
        }

        return dbPMap;
    }


    /**
     * 将Bean集合转换为Map形式：key为Long型，value为List<bean>对象
     *
     * @param list         需要转换的集合
     * @param keyFieldName 作为key的字段名称
     * @param <T>          对象类型
     * @return
     */
    public static <T> Map<Long, List<T>> transBean2ListMap(List<T> list, String keyFieldName) {
        Map<Long, List<T>> dbPMap = new HashMap<Long, List<T>>();

        if (CollectionUtils.isEmpty(list)) {
            return dbPMap;
        }

        String keyValue = null;
        Long keyLong = null;
        try {
            for (T obj : list) {
                keyValue = BeanUtils.getProperty(obj, keyFieldName);

                if (StringUtils.isBlank(keyValue)) {
                    continue;
                }

                keyLong = Long.valueOf(keyValue);

                if (CollectionUtils.isEmpty(dbPMap.get(keyLong))) {
                    dbPMap.put(keyLong, new ArrayList<T>());
                }

                dbPMap.get(keyLong).add(obj);
            }
        } catch (Exception e) {
            throw new BaseException("数据转换异常", e.getMessage());
        }

        return dbPMap;
    }

    public static <T> Map<Integer, List<T>> transBean2IntegerListMap(List<T> list, String keyFieldName) {
        Map<Integer, List<T>> dbPMap = new HashMap<Integer, List<T>>();

        if (CollectionUtils.isEmpty(list)) {
            return dbPMap;
        }

        String keyValue = null;
        Integer keyLong = null;
        try {
            for (T obj : list) {
                keyValue = BeanUtils.getProperty(obj, keyFieldName);

                if (StringUtils.isBlank(keyValue)) {
                    continue;
                }

                keyLong = Integer.valueOf(keyValue);

                if (CollectionUtils.isEmpty(dbPMap.get(keyLong))) {
                    dbPMap.put(keyLong, new ArrayList<T>());
                }

                dbPMap.get(keyLong).add(obj);
            }
        } catch (Exception e) {
            throw new BaseException("数据转换异常", e.getMessage());
        }

        return dbPMap;
    }

    /**
     * 将Bean集合转换为Map形式：key为String型，value为List<bean>对象
     *
     * @param list         需要转换的集合
     * @param keyFieldName 作为key的字段名称
     * @param <T>          对象类型
     * @return
     */
    public static <T> Map<String, List<T>> transBean2StringListMap(List<T> list, String keyFieldName) {
        Map<String, List<T>> dbPMap = new HashMap<String, List<T>>();

        if (CollectionUtils.isEmpty(list)) {
            return dbPMap;
        }

        String keyValue = null;
        try {
            for (T obj : list) {
                keyValue = BeanUtils.getProperty(obj, keyFieldName);

                if (StringUtils.isBlank(keyValue)) {
                    continue;
                }

                if (CollectionUtils.isEmpty(dbPMap.get(keyValue))) {
                    dbPMap.put(keyValue, new ArrayList<T>());
                }

                dbPMap.get(keyValue).add(obj);
            }
        } catch (Exception e) {
            throw new BaseException("数据转换异常", e.getMessage());
        }

        return dbPMap;
    }

    /**
     * 通过id属性转为map，map的key为id属性
     *
     * @param list
     * @param <T>
     * @return
     * @throws IllegalAccessException
     * @throws NoSuchMethodException
     * @throws InvocationTargetException
     */
    public static <T> Map<Long, T> transListToMapById(List<T> list) {
        return transListToMap(list, "id", Long.class);
    }

    /**
     * 将实体列表转为map结构
     *
     * @param list
     * @param keyFieldName
     * @param keyFiledClazz
     * @param <T>           List元素的类型
     * @param <C>           keyFiled的类型
     * @return
     */
    public static <T, C> Map<C, T> transListToMap(List<T> list, final String keyFieldName, final Class<C> keyFiledClazz) {

        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        Map<C, T> transMap = Maps.uniqueIndex(list,
                new Function<T, C>() {
                    @Override
                    public C apply(T from) {
                        String fieldValue = null;
                        try {
                            fieldValue = BeanUtils.getProperty(from, keyFieldName);
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        } catch (InvocationTargetException e) {
                            e.printStackTrace();
                        } catch (NoSuchMethodException e) {
                            e.printStackTrace();
                        }

                        if (keyFiledClazz == Long.class) {
                            return (C) new Long(fieldValue);

                        } else if (keyFiledClazz == String.class) {
                            return (C) fieldValue;

                        } else if (keyFiledClazz == Integer.class) {
                            return (C) new Integer(fieldValue);

                        } else {
                            throw new IllegalClassException("无效的类型：" + keyFiledClazz);
                        }
                    }
                }
        );

        return transMap;
    }


    /**
     * 获取map的value值
     *
     * @param map
     * @param <T>
     * @return
     */
    public static <T> List<T> fetchMapValues(Map<?, T> map) {
        List<T> resultList = new ArrayList<T>();

        if (MapUtils.isEmpty(map)) {
            return resultList;
        }

        for (T oneRtv : map.values()) {
            resultList.add(oneRtv);
        }

        return resultList;
    }

    /**
     * 获取map的key值
     *
     * @param map
     * @param <T>
     * @return
     */
    public static <T> List<T> fetchMapKeys(Map<T, ?> map) {
        List<T> resultList = new ArrayList<T>();

        if (MapUtils.isEmpty(map)) {
            return resultList;
        }

        for (T oneRtv : map.keySet()) {
            resultList.add(oneRtv);
        }

        return resultList;
    }

}
