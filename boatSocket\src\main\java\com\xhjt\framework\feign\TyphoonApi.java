package com.xhjt.framework.feign;

import com.xhjt.project.typhoon.domain.Typhoon;
import com.xhjt.project.typhoon.domain.TyphoonInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
 * @program: meteorology
 * @description: feign
 * @author: Mrs.<PERSON>
 * @create: 2022-03-02 17:01
 **/
@FeignClient(name = "typhoon",url = "${feign.typhoonUrl}")
public interface TyphoonApi {
    @GetMapping("/syncHis")
    void syncHis(@RequestParam(value = "year")Integer year);
    @GetMapping("/getTyphoonData")
    String selectTyphoonList(@RequestParam(value = "yearsTime") Integer yearsTime);
    @GetMapping("/getTyphoonPoint")
    String selectTyphoonPoint(@RequestParam(value = "tfId") Integer tfId);


}
