package com.xhjt.project.ship.controller;

import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.project.ship.domain.CruiseEntity;
import com.xhjt.project.ship.service.CruiseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 航次信息 信息操作处理
 *
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/api/shipCruise")
public class CruiseController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(CruiseController.class);
    @Autowired
    private CruiseService cruiseService;

    /**
     * 获取最新航次
     */
    @GetMapping("/queryVoyageInfo/{sn}/{presentTime}")
    public CruiseEntity queryVoyageInfo(@PathVariable("sn") String sn, @PathVariable("presentTime") Long presentTime) {
        List<CruiseEntity> list = cruiseService.selectCruiseList(sn, presentTime);
         if (list == null || list.size() == 0) {
             return new CruiseEntity();
        }
        return list.get(0);
    }

    @GetMapping("/list/{sn}")
    public List<CruiseEntity> list(@PathVariable("sn") String sn) {
        CruiseEntity cruise = new CruiseEntity();
        cruise.setSn(sn);
        return cruiseService.selectAllCruiseList(cruise);
    }


}
