//通道号
let channelCode;

$(function () {
    document.getElementById("submenuDiv").onclick = function (event) {
        $(".submenu").toggle();
        event.stopPropagation();
    };
    document.body.onclick = function (event) {
        $(".submenu").hide();
    };
    getSnapshot();
    //放大图片
    // toAmplifyImg();
});

//获取图片
function getSnapshot() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/snapshot/log/newest/" + getCurrentSn(),
        dataType: "json",
        success: function (result) {
            $('.showImgDiv').html('');
            let htmlStr = createHtml(result.data);
            $('.showImgDiv').html(htmlStr);
            initSwiper();
            toAmplifyImg();
        },
    });
    refreshSnapshot();
}

function refreshSnapshot() {
    setTimeout(getSnapshot, 60 * 1000);
}

function getSnapshotPath(dir, fileName) {
    return dir + '/' + fileName;
}

//点击图片放大
function toAmplifyImg() {
    $(".showImg").click(function (event) {
        $(".camera").attr("src", event.target.src);
        $("#myModal").modal({backdrop: 'static', keyboard: false});

        channelCode = event.target.getAttribute('data_channelCode');
        //全屏回放
        toFullScreen();
    });
    //
    $(".modal-body").mousemove(function (e) {
        $('#modalHeard').show();
    });
    $(".modal-body").mouseout(function (e) {
        $('#modalHeard').hide();
    });
}

//全屏查看图片回放
function toFullScreen() {
    $(".play").click(function () {
        let url = "../playback.html?" + channelCode + "?ran=" + Math.random();
        open(url);
    });
}

function createHtml(data) {
    if (data.length === 0) {
        return '';
    }
    let htmlStr = '<div class="swiper mySwiper"><div class="swiper-wrapper">';

    for (let i = 0; i < data.length; i++) {
        if (i % 6 === 0) {
            htmlStr += '<div class="swiper-slide"><div class="container">';
        }
        if (i % 3 === 0) {
            htmlStr += '<div class="row" style="margin-bottom: 3em;letter-spacing: 0.16em;margin-left: 5em">';
        }
        htmlStr += '<div class="col-md-3" style="width: 31vw;">\n' +
            '       <div class="footItems_title showTitle">' + data[i].channelName + '</div>\n' +
            '       <div class="footItems_cont"><img class="showImg" src="' + getSnapshotPath(data[i].directory, data[i].fileName) + '" data_channelCode="' + data[i].channelCode + '" alt=""/></div></div>';

        if (i % 3 === 2 || data.length === i + 1) {
            htmlStr += '</div>';
        }
        if (i % 6 === 5 || data.length === i + 1) {
            htmlStr += '</div></div>';
        }
    }

    htmlStr += '</div><div class="swiper-button-next"></div><div class="swiper-button-prev"></div></div>';

    return htmlStr;
}

function initSwiper() {
    let swiper = new Swiper(".mySwiper", {
        slidesPerView: 'auto',
        spaceBetween: 30,
        centeredSlides: true,
        autoplay: {
            delay: 3500
        },
        pagination: {
            el: ".swiper-pagination",
            clickable: true,
        },
        navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
        }
    });
}



