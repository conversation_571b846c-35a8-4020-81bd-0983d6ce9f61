package com.xhjt.project.snapshot.service;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotLogEntity;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.snapshot.mapper.SnapshotLogMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 快照截图记录 实现类
 *
 * <AUTHOR>
 */
@Service
public class SnapshotLogService {

    @Autowired
    private SnapshotLogMapper snapshotLogMapper;


    @Transactional(rollbackFor = Exception.class)
    @DataScope(deptAlias = "d")
    public List<SnapshotLogEntity> selectSnapshotLogList(SnapshotLogEntity snapshotLog) {
        return snapshotLogMapper.selectSnapshotLogList(snapshotLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotLogEntity selectSnapshotLogById(Long id) {
        SnapshotLogEntity snapshotLog = new SnapshotLogEntity();
        snapshotLog.setId(id);
        return snapshotLogMapper.selectSnapshotLog(snapshotLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<SnapshotLogEntity> selectNewest(String sn) {
        return snapshotLogMapper.selectNewest(sn);
    }
}
