package com.xhjt.project.monitor.domain;

import com.xhjt.framework.aspectj.lang.annotation.Excel;
import com.xhjt.dctcore.commoncore.domain.BaseEntity;

/**
 * 接收数据记录表
 *
 * <AUTHOR>
 */
public class ReceiveLog {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * sn号--服务器
     */
    private String sn;

    /**
     * 记录时间
     */
    @Excel(name = "记录时间")
    private Long recordTime;

    /**
     * 总长度
     */
    @Excel(name = "总长度")
    private Integer totalLength;

    /**
     * 总条数
     */
    @Excel(name = "总条数")
    private Integer totalLines;

    /**
     * 补数据长度
     */
    @Excel(name = "补数据长度")
    private Integer repairLength;

    /**
     * 图片数据长度
     */
    @Excel(name = "图片数据长度")
    private Integer picLength;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getTotalLength() {
        return totalLength;
    }

    public void setTotalLength(Integer totalLength) {
        this.totalLength = totalLength;
    }

    public Integer getTotalLines() {
        return totalLines;
    }

    public void setTotalLines(Integer totalLines) {
        this.totalLines = totalLines;
    }

    public Integer getRepairLength() {
        return repairLength;
    }

    public void setRepairLength(Integer repairLength) {
        this.repairLength = repairLength;
    }

    public Integer getPicLength() {
        return picLength;
    }

    public void setPicLength(Integer picLength) {
        this.picLength = picLength;
    }

    public Long getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Long recordTime) {
        this.recordTime = recordTime;
    }
}
