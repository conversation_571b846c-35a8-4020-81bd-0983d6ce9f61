package com.xhjt.project.ship.controller;

import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.project.ship.domain.CruiseStationEntity;
import com.xhjt.project.ship.service.CruiseStationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 航次站位信息 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/cruiseStation")
public class CruiseStationController extends BaseController {

    @Autowired
    private CruiseStationService cruiseStationService;

    /**
     * 获取航次站位信息列表
     */
    @GetMapping("/list")
    public AjaxResult list(CruiseStationEntity cruiseStation) {
        List<CruiseStationEntity> list = cruiseStationService.selectCruiseStationListByDept(cruiseStation);
        return AjaxResult.success("查询成功", list);
    }

    /**
     * 根据参数编号获取详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(cruiseStationService.selectCruiseStationById(id));
    }

}
