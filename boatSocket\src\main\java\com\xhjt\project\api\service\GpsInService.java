package com.xhjt.project.api.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.domain.hbase.GpsInHbaseVo;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * gps 操作类
 */
@Service
@Transactional(readOnly = true)
public class GpsInService {

    private Logger logger = LoggerFactory.getLogger(GpsInService.class);

    private static double EARTH_RADIUS = 6378.137;

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;
    @Autowired
    private RealtimeService realtimeService;

    public List<List<Double>> getGpsData(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {
        if (endTime > System.currentTimeMillis()) {
            endTime = System.currentTimeMillis();
        }

        List<GpsInHbaseVo> list = queryByTime(sn, deviceCode, interval, startTime, endTime);
        if (list == null){
            return null;
        }
        List<List<Double>> result = Lists.newArrayList();

        //取得有效的初始位置
        GpsInHbaseVo firstGps = null;
        for (int i = 0; i < list.size(); i++) {
            if (StringUtils.isBlank(list.get(i).getLongitude()) || "null".equals(list.get(i).getLongitude())
                    || StringUtils.isBlank(list.get(i).getLatitude()) || "null".equals(list.get(i).getLatitude())
                    || StringUtils.isBlank(list.get(i).getInitialTime()) || "null".equals(list.get(i).getInitialTime())
                    || StringUtils.isBlank(list.get(i).getGroundRate()) || "null".equals(list.get(i).getGroundRate())) {
                continue;
            }
            firstGps = list.get(i);
            break;
        }
        if (firstGps == null) {
            return result;
        }

        //获取最新的位置，并添加到轨迹中
        Object object = realtimeService.getLatestData(sn,deviceCode);
        if(object != null) {
            GpsInHbaseVo latestGpsHbaseVo = JSONObject.parseObject(object.toString(), GpsInHbaseVo.class);
            list.add(latestGpsHbaseVo);
        }

        List<Double> gpsList;
        for (GpsInHbaseVo gps : list) {
            if (StringUtils.isBlank(gps.getLongitude()) || "null".equals(gps.getLongitude())
                    || StringUtils.isBlank(gps.getLatitude()) || "null".equals(gps.getLatitude())
                    || StringUtils.isBlank(gps.getInitialTime()) || "null".equals(gps.getInitialTime())
                    || StringUtils.isBlank(gps.getGroundRate()) || "null".equals(gps.getGroundRate())) {
                continue;
            }
            gpsList = Lists.newArrayList();
            gpsList.add(Double.valueOf(gps.getLongitude()));
            gpsList.add(Double.valueOf(gps.getLatitude()));
            gpsList.add(Double.valueOf(gps.getInitialTime()));
            gpsList.add(Double.valueOf(gps.getGroundRate()));
            // 添加与初始位置的距离
            double distance = countDistance(Double.valueOf(gps.getLatitude()), Double.valueOf(gps.getLongitude()), Double.valueOf(firstGps.getLatitude()),
                    Double.valueOf(firstGps.getLongitude()));
            gpsList.add(distance);

            result.add(gpsList);
        }

        return result;
    }

    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public List<GpsInHbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.GPS_IN.getAlias(), deviceCode, interval);
         logger.error("sn--{}-----{}---{}---{}---",sn,startTime,endTime,tableName);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);

        return hBaseDaoUtil.scanByRowList(new GpsInHbaseVo(), tableName, rowList);
    }

    public GpsInHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.GPS_IN.getAlias(), deviceCode, 0);
        return hBaseDaoUtil.getLatestRow(new GpsInHbaseVo(), tableName);
    }

    /**
     * 抽稀数据
     *
     * @param gpsHbaseVoList
     * @param zoomLevel
     * @return
     */
    private List<GpsInHbaseVo> vacuumingData(List<GpsInHbaseVo> gpsHbaseVoList, Integer zoomLevel) {
        Calendar calender = Calendar.getInstance();
        List<GpsInHbaseVo> list = Lists.newArrayList();

        if (gpsHbaseVoList.size() < 100) {
            list.addAll(gpsHbaseVoList);
            return list;
        }

        for (GpsInHbaseVo gpsHbaseVo : gpsHbaseVoList) {
            calender.setTimeInMillis(Long.valueOf(gpsHbaseVo.getInitialTime()));

            boolean isNeed = isNeed(calender, zoomLevel);

            if (isNeed) {
                list.add(gpsHbaseVo);
            }
        }

        return list;
    }

    /**
     * 判断该时间是否是需要的数据
     *
     * @param calender
     * @param zoomLevel
     * @return
     */
    private boolean isNeed(Calendar calender, Integer zoomLevel) {
        int hour = calender.get(Calendar.HOUR_OF_DAY);
        int minute = calender.get(Calendar.MINUTE);
        boolean isNeed = false;
        switch (zoomLevel) {
            case 5:
                if (minute == 0 && hour % 2 == 0) {
                    isNeed = true;
                }
                break;
            case 6:
                if (minute == 0 && hour % 2 == 0) {
                    isNeed = true;
                }
                break;
            case 7:
                if (minute == 0) {
                    isNeed = true;
                }
                break;
            case 8:
                if (minute == 30 || minute == 0) {
                    isNeed = true;
                }
                break;
            case 9:
                if (minute == 0 || minute == 15 || minute == 30 || minute == 45) {
                    isNeed = true;
                }
                break;
            case 10:
                if (minute == 0 || minute == 15 || minute == 30 || minute == 45) {
                    isNeed = true;
                }
                break;
            default:
                break;
        }
        return isNeed;
    }


    /**
     * 计算坐标间的距离，单位为海里
     *
     * @param lat1
     * @param lng1
     * @param lat2
     * @param lng2
     * @return
     */
    public double countDistance(double lat1, double lng1, double lat2, double lng2) {
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lng1) - rad(lng2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        s = s / 1.852;
        s = Math.round(s * 1000d) / 1000d;
        return s;
    }

    private double rad(double d) {
        return d * Math.PI / 180.0;
    }
}
