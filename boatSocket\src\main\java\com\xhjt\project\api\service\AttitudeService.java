package com.xhjt.project.api.service;

import com.xhjt.dctcore.commoncore.domain.hbase.AttitudeHbaseVo;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * attitude 操作类
 */
@Service
@Transactional(readOnly = true)
public class AttitudeService {

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    public AttitudeHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.ATTITUDE.getAlias(), deviceCode, 0);
        return hBaseDaoUtil.getLatestRow(new AttitudeHbaseVo(), tableName);
    }
}
