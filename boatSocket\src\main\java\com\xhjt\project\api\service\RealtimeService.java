package com.xhjt.project.api.service;

import com.alibaba.fastjson.JSONObject;
import com.xhjt.common.utils.StringUtils;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.project.common.RedisParameter;
import com.xhjt.project.device.domain.DeviceEntity;
import com.xhjt.project.device.service.DeviceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * aws 操作类
 */
@Service
@Transactional(readOnly = true)
public class RealtimeService {
    private Logger logger = LoggerFactory.getLogger(RealtimeService.class);

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private AwsService awsService;
    @Autowired
    private Co2Service co2Service;
    @Autowired
    private CompassService compassService;
    @Autowired
    private Ea600Service ea600Service;
    @Autowired
    private GpsService gpsService;
    @Autowired
    private LogService logService;
    @Autowired
    private Sbe21Service sbe21Service;
    @Autowired
    private WindService windService;
    @Autowired
    private GpsInService gpsInService;
    @Autowired
    private CompassInService compassInService;

    /**
     * 获取数据公用方法
     */
    public String getReidsNewData(String sn, String deviceCode) {
        String jsonString = null;
        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        if (redisTemplate.hasKey(RedisParameter.LATEST_DATA + sn + "_" + deviceCode)) {
            jsonString = valueOperations.get(RedisParameter.LATEST_DATA + sn + "_" + deviceCode);
        }
        return jsonString;
    }

    public Object getLatestData(String sn, String deviceCode) {
        String str = getReidsNewData(sn, deviceCode);

        if (StringUtils.isNotBlank(str)) {
            return JSONObject.parseObject(str);
        }

        DeviceEntity device = deviceService.getDeviceBySnAndCode(sn, deviceCode);
        if (device == null) {
            return null;
        }
        Object object = null;

        if (DeviceTypeEnum.AWS.getValue().equals(device.getType())) {
            object = awsService.getLatestDataFromHbase(sn, deviceCode);
        }
        if (DeviceTypeEnum.GO8050.getValue().equals(device.getType())) {
            object = co2Service.getLatestDataFromHbase(sn, deviceCode);
        }
        if (DeviceTypeEnum.COMPASS.getValue().equals(device.getType())) {
            object = compassService.getLatestDataFromHbase(sn, deviceCode);
        }
        if (DeviceTypeEnum.EA600.getValue().equals(device.getType())) {
            object = ea600Service.getLatestDataFromHbase(sn, deviceCode);
        }
        if (DeviceTypeEnum.GPS.getValue().equals(device.getType())) {
            object = gpsService.getLatestDataFromHbase(sn, deviceCode);
        }
        if (DeviceTypeEnum.LOG.getValue().equals(device.getType())) {
            object = logService.getLatestDataFromHbase(sn, deviceCode);
        }
        if (DeviceTypeEnum.SBE21.getValue().equals(device.getType())) {
            object = sbe21Service.getLatestDataFromHbase(sn, deviceCode);
        }
        if (DeviceTypeEnum.WIND.getValue().equals(device.getType())) {
            object = windService.getLatestDataFromHbase(sn, deviceCode);
        }
        if (DeviceTypeEnum.GPS_IN.getValue().equals(device.getType())) {
            object = gpsInService.getLatestDataFromHbase(sn, deviceCode);
        }
        if (DeviceTypeEnum.COMPASS_IN.getValue().equals(device.getType())) {
            object = compassInService.getLatestDataFromHbase(sn, deviceCode);
        }

        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        valueOperations.set(RedisParameter.LATEST_DATA + sn + "_" + deviceCode, JSONObject.toJSONString(object), 3, TimeUnit.MINUTES);

        return object;
    }
}
