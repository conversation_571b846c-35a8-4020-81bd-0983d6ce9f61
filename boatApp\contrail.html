<html>

<head>
    <title>移动实时轨迹</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <script type="text/javascript" src="js/other/jquery/jquery-3.3.1.min.js"></script>
    <script src="js/common/host.js"></script>
    <script src="js/common/mapCommon.js"></script>
    <link rel="stylesheet" href="css/bigemap/MarkerCluster.Default.css" type="text/css"/>
    <link rel="stylesheet" href="css/bigemap/meteorology.css" type="text/css"/>
    <script type="text/javascript">
        let nowTime = new Date().getTime();
        document.write('<link href="css/mapCss/style2.css?timestamp=' + nowTime + '" rel="stylesheet" type="text/css"/>');

        document.write('<link href="'+mapHost+'/bigemap.js/v2.1.0/bigemap.css" rel="stylesheet" type="text/css"/>');
        document.write('<script src="'+mapHost+'/bigemap.js/v2.1.0/bigemap.js" type="text/javascript" ><\/script>');
    </script>
    <script type="text/javascript" src="js/bigemap/bootstrap.min.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/patternUtils.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/symbol.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/polyline_arrow.js"></script>
    <script type="text/javascript" src="js/bigemap/arrow/rotate_marker.js"></script>
    <script type="text/javascript" src="js/bigemap/track/moveMarker.js"></script>
    <script type="text/javascript" src="js/bigemap/track/bm.geometryutil.js"></script>
    <script type="text/javascript" src="js/bigemap/bm.markercluster-src.js"></script>
    <script type="text/javascript" src="js/other/jquery/jquery.cookie.js"></script>
    <script src="js/common/auth.js"></script>
</head>

<body>
<div id="restore" class="huifu btn_weather1 div_btn"  title="暂停自动缩放" style=" background-size: 95%;background-color: #fff;"></div>

<!-- 地图底图 -->
<div id="map" style="cursor:default">
</div>

<!-- 查询传播信息 -->
<script src="js/common/commonFun.js"></script>
<script type="text/javascript" src="js/main/indexMap.js"></script>
</body>

</html>
