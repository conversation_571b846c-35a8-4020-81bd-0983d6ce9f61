package com.xhjt.project.snapshot.service;

import com.xhjt.dctcore.commoncore.domain.snapshot.SnapshotChannelEntity;
import com.xhjt.framework.aspectj.lang.annotation.DataScope;
import com.xhjt.project.snapshot.mapper.SnapshotChannelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 通道配置 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class SnapshotChannelService {

    @Autowired
    private SnapshotChannelMapper snapshotChannelMapper;

    @Transactional(rollbackFor = Exception.class)
    @DataScope(deptAlias = "d")
    public List<SnapshotChannelEntity> selectSnapshotChannelList(SnapshotChannelEntity snapshotChannelEntity) {
        return snapshotChannelMapper.selectSnapshotChannelList(snapshotChannelEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    public SnapshotChannelEntity selectSnapshotChannelById(Long id) {
        SnapshotChannelEntity snapshotChannel = new SnapshotChannelEntity();
        snapshotChannel.setId(id);
        return snapshotChannelMapper.selectSnapshotChannel(snapshotChannel);
    }
}
