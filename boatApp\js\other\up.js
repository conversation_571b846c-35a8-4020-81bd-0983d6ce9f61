!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.VueCountUp=t():e.VueCountUp=t()}(this,function(){return function(e){function t(a){if(n[a])return n[a].exports;var i=n[a]={i:a,l:!1,exports:{}};return e[a].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var n={};return t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,a){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:a})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=3)}([function(e,t,n){"use strict";function a(e){return e&&e.__esModule?e:{default:e}}function i(e){return new u.default(e.$el,e.startValue,e.endValue,e.decimals,e.duration,e.options)}Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=n(6),u=a(o),s=n(5),l=a(s),d=n(1),c=n(4),m={useEasing:!0,useGrouping:!0,separator:",",decimal:".",prefix:"",suffix:""},f=[Number,String],p=[String,Array];t.default={props:{tag:{type:String,default:"span"},startValue:{type:f,default:0},endValue:{type:f,default:0},decimals:{type:f,default:0},duration:{type:f,default:2},options:{type:Object,default:function(){return(0,l.default)({},m)}},immediate:{type:Boolean,default:!0},delay:{type:f,default:0},animateClass:{type:p,required:!1},animatedClass:{type:String,default:"animated"}},data:function(){return{oldVal:null,delayTimeout:null}},computed:{computedClass:function(){var e=this.animateClass;return"string"==typeof e&&""!==e?e.includes(" ")?e.split(" "):e:Array.isArray(e)?e:null}},mounted:function(){this.createCountUp(),this.immediate?this.start():this.jumpToEndValue()},methods:{createCountUp:function(){this._countup&&(this._countup.reset(),this._countup=null),this.delayTimeout&&clearTimeout(this.delayTimeout),this._countup=i(this)},jumpToEndValue:function(){this._countup&&this._countup.reset(),this.delayTimeout&&clearTimeout(this.delayTimeout),this.$el.innerText?this.$el.innerText=this.endValue:this.$el.textContent&&(this.$el.innerText=this.endValue)},recreateCountUp:function(){this.createCountUp(),this.immediate&&this.start()},start:function(){function e(){n.$el.removeEventListener(c.animationEnd,e)}function t(){n.computedClass&&!(0,d.hasClass)(n.$el,n.computedClass)&&((0,d.hasClass)(n.$el,n.animatedClass)||((0,d.addClass)(n.$el,n.animatedClass),n.$el.offsetWidth),n.$el.addEventListener(c.animationEnd,e,!1),(0,d.addClass)(n.$el,n.computedClass)),n._countup.start(function(t){n.computedClass&&(0,d.hasClass)(n.$el,n.computedClass)&&((0,d.removeClass)(n.$el,n.computedClass),n.$el.removeEventListener(c.animationEnd,e)),n.$emit("callback",n,n._countup)})}var n=this,a=Math.max(+this.delay,0);a>0?(this.delayTimeout&&clearTimeout(this.delayTimeout),this.delayTimeout=setTimeout(t,1e3*a)):t()},update:function(e){if(e=Number(e),isNaN(e))return void console.error("[vue-countupjs] update() Error! the val is not validate number");e!==this.oldVal&&(this.oldVal=e,this.computedClass&&!(0,d.hasClass)(this.$el,this.computedClass)&&(0,d.addClass)(this.$el,this.computedClass),this._countup.update(e))},pauseResume:function(){this._countup.pauseResume()},reset:function(){this._countup.reset()}},render:function(e){return e(this.tag,{},"")},watch:{startValue:function(e){if(e=Number(e),isNaN(e))return void console.warn("[vue-countupjs] Warning ! the prop startValue is not number");this.recreateCountUp()},endValue:function(e){if(e=Number(e),isNaN(e))return void console.error("[vue-countupjs] Error! endValue is not number");this.recreateCountUp()},decimals:function(e){return e=Number(e),isNaN(e)?void console.error("[vue-countupjs] Error! decimals is not number"):e>20?void console.error("[vue-countupjs] Error! decimals over limit, the max decimals is 20"):void this.recreateCountUp()},duration:function(e){if(e=Number(e),isNaN(e))return void console.error("[vue-countupjs] Error! duration is not number");this.recreateCountUp()},delay:function(e){if(e=Number(e),isNaN(e))return void console.error("[vue-countupjs] Error! delay is not number");this.recreateCountUp()},options:{deep:!0,handler:function(e){var t=this._countup;if(e&&"object"===(void 0===e?"undefined":r(e)))for(var n in t.options)e.hasOwnProperty(n)&&null!==e[n]&&(t.options[n]=e[n]);""===t.options.separator&&(t.options.useGrouping=!1)}}}}},function(e,t,n){"use strict";function a(e,t){if(!e||!t)return!1;if(Array.isArray(t)){return-1===t.map(function(t){return a(e,t)}).indexOf(!1)}if(-1!==t.indexOf(" "))throw new Error("className should not contain space.");return e.classList?e.classList.contains(t):(" "+e.className+" ").indexOf(" "+t+" ")>-1}function i(e,t){if(e){for(var n=e.className,i=Array.isArray(t)?t:(t||"").split(" "),r=0,o=i.length;r<o;r++){var u=i[r];u&&(e.classList?e.classList.add(u):a(e,u)||(n+=" "+u))}e.classList||(e.className=n)}}function r(e,t){if(e&&t){for(var n=Array.isArray(t)?t:t.split(" "),i=" "+e.className+" ",r=0,u=n.length;r<u;r++){var s=n[r];s&&(e.classList?e.classList.remove(s):a(e,s)&&(i=i.replace(" "+s+" "," ")))}e.classList||(e.className=o(i))}}Object.defineProperty(t,"__esModule",{value:!0}),t.hasClass=a,t.addClass=i,t.removeClass=r;var o=function(e){return(e||"").replace(/^[\s\uFEFF]+|[\s\uFEFF]+$/g,"")};t.inBrowser="undefined"!=typeof window},function(e,t,n){"use strict";function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!a.installed){var n=e.util.extend({},o,t);e.component(n.name,r.default),a.installed=!0}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=a;var i=n(0),r=function(e){return e&&e.__esModule?e:{default:e}}(i),o={name:"v-countup"}},function(e,t,n){"use strict";function a(e){return e&&e.__esModule?e:{default:e}}var i=n(0),r=a(i),o=n(2),u=a(o),s=n(1);r.default.version="1.0.0",r.default.install=u.default,e.exports=r.default,s.inBrowser&&window.Vue&&window.Vue.use(r.default)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.animationEnd=function(){for(var e=document.createElement("div").style,t=["a","webkitA","MozA","OA","msA"],n=["animationend","webkitAnimationEnd","animationend","oAnimationEnd","MSAnimationEnd"],a=0,i=t.length;a<i;a++)if(t[a]+"nimation"in e)return n[a];return"animationend"}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){if(e instanceof Object==!0)return null;for(var t=Array.prototype.slice.call(arguments,1),n=0,a=t.length;n<a;n++){var i=t[n];for(var r in i)e[r]=i[r]}return e}},function(e,t,n){var a,i;!function(r,o){a=o,void 0!==(i="function"==typeof a?a.call(t,n,t,e):a)&&(e.exports=i)}(0,function(e,t,n){return function(e,t,n,a,i,r){function o(e){e=e.toFixed(m.decimals),e+="";var t,n,a,i;if(t=e.split("."),n=t[0],a=t.length>1?m.options.decimal+t[1]:"",i=/(\d+)(\d{3})/,m.options.useGrouping)for(;i.test(n);)n=n.replace(i,"$1"+m.options.separator+"$2");return m.options.prefix+n+a+m.options.suffix}function u(e,t,n,a){return n*(1-Math.pow(2,-10*e/a))*1024/1023+t}function s(e){return"number"==typeof e&&!isNaN(e)}for(var l=0,d=["webkit","moz","ms","o"],c=0;c<d.length&&!window.requestAnimationFrame;++c)window.requestAnimationFrame=window[d[c]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[d[c]+"CancelAnimationFrame"]||window[d[c]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(e,t){var n=(new Date).getTime(),a=Math.max(0,16-(n-l)),i=window.setTimeout(function(){e(n+a)},a);return l=n+a,i}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){clearTimeout(e)});var m=this;if(m.version=function(){return"1.8.5"},m.options={useEasing:!0,useGrouping:!0,separator:",",decimal:".",easingFn:u,formattingFn:o,prefix:"",suffix:""},r&&"object"==typeof r)for(var f in m.options)r.hasOwnProperty(f)&&null!==r[f]&&(m.options[f]=r[f]);""===m.options.separator&&(m.options.useGrouping=!1),m.initialize=function(){return!!m.initialized||(m.d="string"==typeof e?document.getElementById(e):e,m.d?(m.startVal=Number(t),m.endVal=Number(n),s(m.startVal)&&s(m.endVal)?(m.decimals=Math.max(0,a||0),m.dec=Math.pow(10,m.decimals),m.duration=1e3*Number(i)||2e3,m.countDown=m.startVal>m.endVal,m.frameVal=m.startVal,m.initialized=!0,!0):(console.error("[CountUp] startVal or endVal is not a number",m.startVal,m.endVal),!1)):(console.error("[CountUp] target is null or undefined",m.d),!1))},m.printValue=function(e){var t=m.options.formattingFn(e);"INPUT"===m.d.tagName?this.d.value=t:"text"===m.d.tagName||"tspan"===m.d.tagName?this.d.textContent=t:this.d.innerHTML=t},m.count=function(e){m.startTime||(m.startTime=e),m.timestamp=e;var t=e-m.startTime;m.remaining=m.duration-t,m.options.useEasing?m.countDown?m.frameVal=m.startVal-m.options.easingFn(t,0,m.startVal-m.endVal,m.duration):m.frameVal=m.options.easingFn(t,m.startVal,m.endVal-m.startVal,m.duration):m.countDown?m.frameVal=m.startVal-(m.startVal-m.endVal)*(t/m.duration):m.frameVal=m.startVal+(m.endVal-m.startVal)*(t/m.duration),m.countDown?m.frameVal=m.frameVal<m.endVal?m.endVal:m.frameVal:m.frameVal=m.frameVal>m.endVal?m.endVal:m.frameVal,m.frameVal=Math.round(m.frameVal*m.dec)/m.dec,m.printValue(m.frameVal),t<m.duration?m.rAF=requestAnimationFrame(m.count):m.callback&&m.callback()},m.start=function(e){m.initialize()&&(m.callback=e,m.rAF=requestAnimationFrame(m.count))},m.pauseResume=function(){m.paused?(m.paused=!1,delete m.startTime,m.duration=m.remaining,m.startVal=m.frameVal,requestAnimationFrame(m.count)):(m.paused=!0,cancelAnimationFrame(m.rAF))},m.reset=function(){m.paused=!1,delete m.startTime,m.initialized=!1,m.initialize()&&(cancelAnimationFrame(m.rAF),m.printValue(m.startVal))},m.update=function(e){m.initialize()&&e!==m.frameVal&&(cancelAnimationFrame(m.rAF),m.paused=!1,delete m.startTime,m.startVal=m.frameVal,m.endVal=Number(e),s(m.endVal)?(m.countDown=m.startVal>m.endVal,m.rAF=requestAnimationFrame(m.count)):console.error("[CountUp] update() - new endVal is not a number",e))},m.initialize()&&m.printValue(m.startVal)}})}])});
//# sourceMappingURL=vue-countup.min.js.map