package com.xhjt.project.api.domain;

/**
 * @description:  色带所需实体
 * @author: rr
 * @create: 2020-01-07 12:54
 **/

public class Aggregate {
    /**
     * $WIMWV
     * 相对风向
     */
    private String relativeWind;
    /**
     * 相对风向标识
     */
    private String windLogoR;
    /**
     * 相对风速
     */
    private String relativeWindSpeed;
    /**
     * 真实风向
     */
    private String trueWind;
    /**
     * 真实风速
     */
    private String trueWindSpeed;
    /**
     * 真实风向标识
     */
    private String windLogoT;

    /**
     * $WIXDR
     * 气温值（6个字节）
     */
    private String airTemperature;

    /**
     * 相对湿度数值（4个字节）
     */
    private String humidity;

    /**
     *  露点温度数值（6个字节） 数据带单位
     */
    private String pointTem;
    /**
     *  气压数值（6个字节），数据带单位
     */
    private String pressure;

    //    CO2
    /**
     * 平衡器温度   equ_temp
     */
    private String equTemp;
    /**
     * CO2 检测值co2 (ppm),
     */
    private String co2;
    /**
     * 溶氧饱和度Saturation（%）
     */
    private String saturation;
    /**
     * SBE21原位传感器温度 TSG_temp2（℃）
     */
    private String tsgTemp2;
    /**
     * SBE21 实验室叶绿素值 Chl (mg/m^3)
     */
    private String chl;
    /**
     * 真风速 True_wind_sp(m/s)
     */
    private String trueWindSp;
    /**
     * CH4检测值 CH4(ppm)
     */
    private String ch4;

    /**
     * water depth (in feet) 水深
     */
    private String  waterDepthF;




    /**
     * SBE21温度
     */
    private String temperature;
    /**
     * 原位温度
     */
    private String situTemperature;
    /**
     * 盐度
     */
    private String salinity;
    /**
     * 溶解氧
     */
    private String dissolvedOxygen;
    /**
     * 叶绿素
     */
    private String chlorophyll;
    /**
     * 浊度
     */
    private String turbidity;

    /**
     * 录入时间
     */
    private String initialTime;
    private String initialBjTime;
    /**
     * 纬度
     */
    private String latitude;
    /**
     * 经度
     */
    private String longitude;


    public String getRelativeWind() {
        return relativeWind;
    }

    public void setRelativeWind(String relativeWind) {
        this.relativeWind = relativeWind;
    }

    public String getWindLogoR() {
        return windLogoR;
    }

    public void setWindLogoR(String windLogoR) {
        this.windLogoR = windLogoR;
    }

    public String getRelativeWindSpeed() {
        return relativeWindSpeed;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getTrueWind() {
        return trueWind;
    }

    public void setTrueWind(String trueWind) {
        this.trueWind = trueWind;
    }

    public String getTrueWindSpeed() {
        return trueWindSpeed;
    }

    public void setTrueWindSpeed(String trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public String getWindLogoT() {
        return windLogoT;
    }

    public void setWindLogoT(String windLogoT) {
        this.windLogoT = windLogoT;
    }

    public String getAirTemperature() {
        return airTemperature;
    }

    public void setAirTemperature(String airTemperature) {
        this.airTemperature = airTemperature;
    }

    public String getHumidity() {
        return humidity;
    }

    public void setHumidity(String humidity) {
        this.humidity = humidity;
    }

    public String getPointTem() {
        return pointTem;
    }

    public void setPointTem(String pointTem) {
        this.pointTem = pointTem;
    }

    public String getPressure() {
        return pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getEquTemp() {
        return equTemp;
    }

    public void setEquTemp(String equTemp) {
        this.equTemp = equTemp;
    }

    public String getCo2() {
        return co2;
    }

    public void setCo2(String co2) {
        this.co2 = co2;
    }

    public String getSaturation() {
        return saturation;
    }

    public void setSaturation(String saturation) {
        this.saturation = saturation;
    }

    public String getTsgTemp2() {
        return tsgTemp2;
    }

    public void setTsgTemp2(String tsgTemp2) {
        this.tsgTemp2 = tsgTemp2;
    }

    public String getChl() {
        return chl;
    }

    public void setChl(String chl) {
        this.chl = chl;
    }

    public String getTrueWindSp() {
        return trueWindSp;
    }

    public void setTrueWindSp(String trueWindSp) {
        this.trueWindSp = trueWindSp;
    }

    public String getCh4() {
        return ch4;
    }

    public void setCh4(String ch4) {
        this.ch4 = ch4;
    }

    public String getWaterDepthF() {
        return waterDepthF;
    }

    public void setWaterDepthF(String waterDepthF) {
        this.waterDepthF = waterDepthF;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public String getSituTemperature() {
        return situTemperature;
    }

    public void setSituTemperature(String situTemperature) {
        this.situTemperature = situTemperature;
    }

    public String getSalinity() {
        return salinity;
    }

    public void setSalinity(String salinity) {
        this.salinity = salinity;
    }

    public String getDissolvedOxygen() {
        return dissolvedOxygen;
    }

    public void setDissolvedOxygen(String dissolvedOxygen) {
        this.dissolvedOxygen = dissolvedOxygen;
    }

    public String getChlorophyll() {
        return chlorophyll;
    }

    public void setChlorophyll(String chlorophyll) {
        this.chlorophyll = chlorophyll;
    }

    public String getTurbidity() {
        return turbidity;
    }

    public void setTurbidity(String turbidity) {
        this.turbidity = turbidity;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
}
