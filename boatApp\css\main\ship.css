* {
    margin: 0;
    padding: 0px;
    box-sizing: border-box;
}

::-webkit-scrollbar {
    width: 0;
}

ul li {
    list-style: none;
    display: inline-block;
}

a {
    text-decoration: none;
}

img {
    max-width: 100%;
    display: block;
}

html,
body {
    width: 100%;
    height: 100%;
    font-size: 1rem;
    font-family: "microsoft yahei", arial;
    color: #C9C5C5;
}

.head {
    background-image: url("../../img/head.png");
    width: 100%;
    height: 7vw;
    position: fixed;
    z-index: 99;
    background-size: 100% 100%;
    display: flex;
    top: 0;
    left: 0;
}

.bg {
    width: 100%;
    position: relative;
}

.leftDiv {
    position: absolute;
    width: 14vw;
    height: 47vw;
    background: rgb(57 60 62 / 20%);
    top: 5.8vw;
    left: 2vw;
}

.title {
    width: 32.08vw;
    height: 4.6875vw;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 4vw;
    font-weight: 700;
    text-align: center;
    letter-spacing: 10px;
    background-image: -webkit-linear-gradient(bottom, #5298ff, #7fefff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.ship {
    position: absolute;
    left: 1.5vw;
    top: 11vw;
    height: 38vw;
    width: 14rem;
    background-image: url("../../img/ship/shipRect.png");
    background-size: 100% 100%
}

.ship h1 {
    font-size: 18px;
    color: #fff;
    margin: 1rem
}

#shipDiv {
    width: 100%;
    font-size: 24px
}

#shipDiv p {
    margin: 1vw;
    cursor: pointer;
}

.condition {
    position: absolute;
    margin: 18vw 1.5vw;
}

.shipCon {
    border: 2px solid #C9C5C5;
    width: 11vw;
    height: 25vw;
    margin-top: 0.5vw;
}


.mainWrapCenter {
    width: 80.2vw;
    height: 80%;
    position: absolute;
    margin: 5vw 17vw;
    top: 1.25vw;
}

.mainWrapCenter_zz {
    border: 3px solid #48a2b3;
    box-shadow: inset 0 0 70px rgba(0, 156, 255, 0.7);
    -webkitbox-shadow: inset 0px 0px 70px rgba(0, 156, 255, 0.7);
    width: 56.3vw;
    height: 33.5vw;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2;
}

.mainWrapCenter img {
    width: 100%;
    height: 33.5vw;
}

.shipList {
    position: absolute;
    border: 1px solid white;
    height: 20vw;
    width: 10vw;
    right: 2vw;
    top: 0.5vw;
    z-index: 99;
}

.leftTop {
    color: #fff;
    font-size: 1vw;
    height: 1vw;
    position: absolute;
    left: 2.6vw;
    top: 0.5vw
}

.localTime {
    position: absolute;
    font-size: 1vw;
    top: 2.2vw;
    left: 14.5vw;
    color: #fff
}

.leftYmd {
    position: absolute;
    top: 2.2vw;
    left: 2.6vw;
    font-size: 1vw;
    color: #8eacdc;
}

.rightYmd {
    font-size: 1vw;
    color: #8eacdc;
    position: absolute;
    height: auto;
    top: 2.2vw;
    right: 1.9vw
}

.serveTitle {
    color: #fff;
    font-size: 1vw;
    position: absolute;
    top: 2.2vw;
    right: 13vw
}

.serverTime {
    color: #fff;
    font-size: 1vw;
    position: absolute;
    top: 0.5vw;
    right: 6.3vw;
}

.info {
    position: absolute;
    width: 14rem;
    height: 37.8vw;
    top: 11vw;
    left: 16.5vw;

}

.baseInfo {
    background-image: url("../../img/ship/infoRect.png");
    background-size: 100% 100%;
    width: 14rem;
    height: 17.8vw;
    margin-bottom: 0.8vw;
    padding: 10px 20px;
}

.baseInfo div {
    height: 13%;
    margin-bottom: 2%;
    display: flex;
    justify-content: space-between;
}
#baseInfo{
    font-size: 15px;
}
.baseInfo div span {
    color: #fff;
    font-size: 18px;

}

.baseInfo div span:last-child {
    color: #fff;
    font-size: 18px;
    }


.baseInfo div h1 {
    color: #fff;
    width: 100%;
}

.imgInfo {
    background-image: url("../../img/ship/channelImg.png");
    background-size: 100% 100%;
    width: 100%;
    height: 25%;
    padding: 10px;
}

/*.imgInfo img{*/
/*    margin: 10px;*/
/*}*/

footer {
    text-align: center;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2vw;
}

footer p {
    font-size: 24px;
    color: #23fafd;
}

.toggleList {
    position: absolute;
    left: 0;
    cursor: pointer;
    top: 45%;
    width: 1.5vw;
    height: 10vw;
    margin: auto 0;
}

.hideText {
    position: absolute;
    text-align: center;
    width: 7vw;
    height: 2vw;
    right: 1rem;
    bottom: 1rem;
    cursor: pointer;

}

.hideText img {
    width: 20px;
    height: 20px;
    margin-top: 5px;
}

.hideBaseInfo {
    font-size: 18px;
    color: #fff;
    margin: 2px 10px;
}

@media screen  and (min-width: 2000px ) {
    .ship{
        width: 12rem;
    }
    .hideText{
        right: 1vw;
        bottom: 1vw;
    }
    .baseInfo {
        width: 12rem;
        left: 12.5vw;
    }
    .info {
        width: 12rem;
        left: 15rem;
    }
    .baseInfo div span{
        font-size: 21px;
    }
    .baseInfo div span:last-child {
        color: #fff;
        font-size: 21px;
        float: right;
    }
    #baseInfo{
        font-size: 18px;
    }

}


