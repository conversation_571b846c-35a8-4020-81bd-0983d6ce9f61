package com.xhjt.project.ship.domain;

import com.xhjt.dctcore.commoncore.domain.BaseEntity;

/**
 * 船只信息
 *
 * <AUTHOR>
 */
public class ShipEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long shipId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 船名
     */
    private String name;

    /**
     * MMSI
     */
    private String mmsi;

    /**
     * 呼号
     */
    private String callSign;

    /**
     * IMO
     */
    private String imo;

    /**
     * sn
     */
    private String sn;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;


    public Long getShipId() {
        return shipId;
    }

    public void setShipId(Long shipId) {
        this.shipId = shipId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMmsi() {
        return mmsi;
    }

    public void setMmsi(String mmsi) {
        this.mmsi = mmsi;
    }

    public String getCallSign() {
        return callSign;
    }

    public void setCallSign(String callSign) {
        this.callSign = callSign;
    }

    public String getImo() {
        return imo;
    }

    public void setImo(String imo) {
        this.imo = imo;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getStatus() {
        return status == null ? 0 : status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }
}
