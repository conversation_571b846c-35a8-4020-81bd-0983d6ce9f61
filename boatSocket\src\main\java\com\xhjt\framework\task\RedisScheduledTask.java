package com.xhjt.framework.task;

import com.xhjt.project.common.CommonService;
import com.xhjt.project.common.RedisParameter;
import com.xhjt.project.ship.domain.RedisObjVo;
import com.xhjt.project.ship.domain.ShipEntity;
import com.xhjt.project.ship.service.RedisObjService;
import com.xhjt.project.ship.service.ShipService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * redis定时任务调度
 *
 * <AUTHOR>
 */
@Component
@EnableScheduling
public class RedisScheduledTask {
    private Logger logger = LoggerFactory.getLogger(RedisScheduledTask.class);
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private ShipService shipService;
    @Autowired
    private RedisObjService redisObjService;
    /**
     * 保存redis-key以及count到数据库
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void saveRedisCount() {
        logger.info("redis保存开始------");
        //获取所有船只，用于循环更新对应的redis记录
        List<String> list = shipService.queryEnableSnList();
        if (list!=null && list.size()>0){
            logger.info("sn号集合---{}",list.toString());
            String sn = "";
            for (String str:list) {
                //赋值sn
                sn = str;
                ValueOperations<String, Integer> opsForValue = redisTemplate.opsForValue();
                // 更新已记录的最大编号
                Integer maxReceiveNum = opsForValue.get(RedisParameter.MAX_RECEIVE_NUM+sn) == null ? 0 : opsForValue.get(RedisParameter.MAX_RECEIVE_NUM+sn);
                logger.info("更新已记录的最大编号---{}-----sn---{}",maxReceiveNum,sn);
                RedisObjVo selectObj = new RedisObjVo();
                selectObj.setRedisKey(RedisParameter.MAX_RECEIVE_NUM+sn);
                RedisObjVo redisObjReceiveVo = redisObjService.selectRedisObj(selectObj);
                if (redisObjReceiveVo != null){
                    redisObjReceiveVo.setValueLength(maxReceiveNum);
                    redisObjService.update(redisObjReceiveVo);
                }else{
                    //保存到数据库
                    redisObjReceiveVo = new RedisObjVo();
                    redisObjReceiveVo.setRedisKey(RedisParameter.MAX_RECEIVE_NUM+sn);
                    redisObjReceiveVo.setSn(sn);
                    redisObjReceiveVo.setUpdateTime(new Date());
                    redisObjReceiveVo.setValueLength(maxReceiveNum);
                    redisObjService.save(redisObjReceiveVo);
                }

                // 查看已检查过的编号
                Integer maxCheckNum = opsForValue.get(RedisParameter.MAX_CHECK_NUM+sn) == null ? 0 : opsForValue.get(RedisParameter.MAX_CHECK_NUM+sn);
                logger.info("查看已检查过的编号---{}-----sn---{}",maxCheckNum,sn);
                selectObj.setRedisKey(RedisParameter.MAX_CHECK_NUM+sn);
                RedisObjVo redisObjCheckVo = redisObjService.selectRedisObj(selectObj);
                if (redisObjCheckVo != null){
                    redisObjCheckVo.setValueLength(maxCheckNum);
                    redisObjService.update(redisObjCheckVo);
                }else{
                    //保存到数据库
                    redisObjCheckVo = new RedisObjVo();
                    redisObjCheckVo.setRedisKey(RedisParameter.MAX_CHECK_NUM+sn);
                    redisObjCheckVo.setSn(sn);
                    redisObjCheckVo.setUpdateTime(new Date());
                    redisObjCheckVo.setValueLength(maxCheckNum);
                    redisObjService.save(redisObjCheckVo);
                }

                //岸上丢失数据集合
                ListOperations<String, Object> opsForSet = redisTemplate.opsForList();
                Long loseDataLength = opsForSet.size(RedisParameter.LOSE_DATA_LIST+sn) == null ? 0 : opsForSet.size(RedisParameter.LOSE_DATA_LIST+sn);
                logger.info("岸上丢失数据集合长度---{}-----sn---{}",loseDataLength,sn);
                selectObj.setRedisKey(RedisParameter.LOSE_DATA_LIST+sn);
                RedisObjVo redisObjloseDataVo = redisObjService.selectRedisObj(selectObj);
                if (redisObjloseDataVo != null){
                    redisObjloseDataVo.setValueLength(loseDataLength.intValue());
                    redisObjService.update(redisObjloseDataVo);
                }else {
                    //保存到数据库
                    redisObjloseDataVo = new RedisObjVo();
                    redisObjloseDataVo.setRedisKey(RedisParameter.LOSE_DATA_LIST+sn);
                    redisObjloseDataVo.setSn(sn);
                    redisObjloseDataVo.setUpdateTime(new Date());
                    redisObjloseDataVo.setValueLength(loseDataLength.intValue());
                    redisObjService.save(redisObjloseDataVo);
                }
            }
        }


    }

}
