package com.xhjt.project.monitor.controller;

import com.xhjt.common.utils.poi.ExcelUtil;
import com.xhjt.framework.aspectj.lang.annotation.Log;
import com.xhjt.framework.aspectj.lang.enums.BusinessType;
import com.xhjt.framework.web.controller.BaseController;
import com.xhjt.framework.web.domain.AjaxResult;
import com.xhjt.framework.web.page.TableDataInfo;
import com.xhjt.project.monitor.domain.ReceiveLog;
import com.xhjt.project.monitor.service.IReceiveLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 接收数据记录
 *
 * <AUTHOR>
 */
@CrossOrigin
@RestController
@RequestMapping("/monitor/receiveLog")
public class ReceiveLogController extends BaseController {
    @Autowired
    private IReceiveLogService receiveLogService;

    @GetMapping("/list/{recordTime}")
    public List<ReceiveLog> list(@PathVariable("recordTime") Long recordTime){
        List<ReceiveLog> list = receiveLogService.selectList(recordTime);
        return list;
    }


}
