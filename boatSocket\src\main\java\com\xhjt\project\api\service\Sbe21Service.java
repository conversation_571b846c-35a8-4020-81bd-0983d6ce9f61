package com.xhjt.project.api.service;

import com.google.common.collect.Lists;
import com.xhjt.dctcore.commoncore.domain.hbase.GpsHbaseVo;
import com.xhjt.dctcore.commoncore.domain.hbase.Sbe21HbaseVo;
import com.xhjt.dctcore.commoncore.enums.DeviceTypeEnum;
import com.xhjt.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.xhjt.project.api.domain.Sbe21Vo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * Sbe21 操作类
 */
@Service
@Transactional(readOnly = true)
public class Sbe21Service {

    private Logger logger = LoggerFactory.getLogger(Sbe21Service.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    @Autowired
    private GpsService gpsService;

    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public List<Sbe21Vo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.SBE21.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);

        List<Sbe21HbaseVo> sbe21HbaseVos = hBaseDaoUtil.scanByRowList(new Sbe21HbaseVo(), tableName, rowList);

        List<GpsHbaseVo> gpsHbaseVoList = gpsService.queryByTime(sn, deviceCode, interval, startTime, endTime);

        return mergeResult(gpsHbaseVoList, sbe21HbaseVos);
    }

    public Sbe21HbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.SBE21.getAlias(), deviceCode, 0);
        return hBaseDaoUtil.getLatestRow(new Sbe21HbaseVo(), tableName);
    }

    /**
     * 把sbe21的每条数据，塞上对应的经纬度
     *
     * @param gpsHbaseVoList
     * @param sbe21HbaseVos
     * @return
     */
    private List<Sbe21Vo> mergeResult(List<GpsHbaseVo> gpsHbaseVoList, List<Sbe21HbaseVo> sbe21HbaseVos) {
        List<Sbe21Vo> list = Lists.newArrayList();
        Sbe21Vo sbe21Vo;
        for (Sbe21HbaseVo sbe21HbaseVo : sbe21HbaseVos) {
            sbe21Vo = new Sbe21Vo();
            BeanUtils.copyProperties(sbe21HbaseVo, sbe21Vo);
            for (GpsHbaseVo gpsHbaseVo : gpsHbaseVoList) {
                if (sbe21HbaseVo.getId().equals(gpsHbaseVo.getId())) {
                    sbe21Vo.setLongitude(Double.valueOf(gpsHbaseVo.getLongitude()));
                    sbe21Vo.setLatitude(Double.valueOf(gpsHbaseVo.getLatitude()));
                }
            }
            list.add(sbe21Vo);
        }
        return list;
    }

}