package com.xhjt.project.ship.service;

import com.xhjt.project.ship.domain.CruiseEntity;

import java.util.List;

/**
 * 航次管理 服务层
 *
 * <AUTHOR>
 */
public interface CruiseService {


    /**
     * 查询航次列表
     *
     * @param cruise 航次信息
     * @return 航次集合
     */
    public List<CruiseEntity> selectAllCruiseList(CruiseEntity cruise);

    /**
     * 查询航次列表
     *
     * @param presentTime 当前时间
     * @return 航次集合
     */
    public List<CruiseEntity> selectCruiseList(String sn, Long presentTime);


}
