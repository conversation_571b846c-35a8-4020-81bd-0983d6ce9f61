package com.xhjt.project.device.mapper;


import com.xhjt.project.device.domain.DeviceEntity;

import java.util.List;

/**
 * 设备管理 数据层
 *
 * <AUTHOR>
 */
public interface DeviceMapper {
    /**
     * 查询设备信息
     *
     * @param device 设备信息
     * @return 设备信息
     */
    public DeviceEntity selectDevice(DeviceEntity device);

    /**
     * 查询设备列表
     *
     * @param device 设备信息
     * @return 设备集合
     */
    public List<DeviceEntity> selectDeviceList(DeviceEntity device);

}