package com.xhjt.project.ship.mapper;

import com.xhjt.project.ship.domain.CruiseEntity;

import java.util.List;


/**
 * 航次管理 数据层
 *
 * <AUTHOR>
 */
public interface CruiseMapper {




    /**
     * 查询航次列表
     *
     * @param cruise 航次信息
     * @return 航次集合
     */
    public List<CruiseEntity> selectAllCruiseList(CruiseEntity cruise);

    /**
     * 查询航次列表
     *
     * @param cruise
     * @return 航次集合
     */
    public List<CruiseEntity> selectCruiseList(CruiseEntity cruise);



}