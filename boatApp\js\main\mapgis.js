let startPoint = [];
//航次开始和结束时间
let cruiseStartTime;
let cruiseEndTime;
let shipName = '智慧船情';
//航次编号
let cruiseCode;
let hehdt;

let map;
let zoomLevel = 6;
//海图卫星图
let satelliteLayer;
let seaLayer;
// 当前的地图图层
let nowLayer;
// 全部的GPS信息
let gpsList = [];
// 全部坐标
let allCoordinate = [];
// 轨迹
let pathPattern;
//小船
let shipMarker;
let moveTime = 0;

let codes = '032A,042A';
let wsUrl = "ws://" + window.location.host + laputaWsHost + "/websocket/" + codes + "/" + getCurrentSn();
let count = 0;

//所有航次
let cruiseList;
//历史回放小船
let hisShipMarker;
//历史回放轨迹
let hisPassedPath;
let drawColor=$.cookie('CurrentColor')
let shipImg=new Map();
let colorArr = ['#FFDB6F', '#23FDF0', '#8AAFEC','#FF8955', '#21a40a'];


$(function () {
    for(let i in colorArr){
        shipImg.set(colorArr[i],'../img/ship/shipList/position'+i+'.png')
    }
    //获取航次信息
    getNewestCruise();
    //连接websocket
    createWebSocket(wsUrl, handleWsData);

    bind();
    //自动缩放地图
    toLessen();
});

function bind() {
    $("#shipName").html(shipName);
    // 轨迹弹出框信息
    $("#locus").on("click", function (e) {
        e.stopPropagation();
        frameSwitch($(this), $("#locusFrame"), 1);
    });
    // 台风弹出框信息
    $("#typhoon").on("click", function (e) {
        e.stopPropagation();
        frameSwitch($(this), $("#typhoonFrame"), 2);
    });
    //历史数据、实时数据切换
    $("#hisData").click(function () {
        $("#nowMsg").toggle();
        let $hisMsg = $("#hisMsg");
        $hisMsg.toggle();
        $("#ribbon").toggle();

        if ($hisMsg.is(':visible')) {
            $('#hisData').html("实时数据");
            initHisFrame();
        } else {
            stopAnimation(false);
            $('#hisData').html("历史数据");
            map.remove();
            getNewestCruise();
            restartWs(wsUrl, handleWsData);
        }
    });
}

// 初始化日期控件
function initLayDate(elem, value, type) {
    let $elem = $('#' + elem);
    let $parent = $elem.parent();
    let html = '<input type="text" style="color: black" class="inputtime"  id="' + elem + '" />';
    $elem.remove();
    $parent.html(html);

    laydate.render({
        elem: '#' + elem,
        type: 'datetime',
        theme: '#314780',
        value: formatDate(value),
        min: cruiseStartTime,
        max: cruiseEndTime,
        trigger: 'click',
        btns: ['confirm'],
        done: function (value, date) {
            let selTime = new Date(value).getTime();
            if (type === 1 && cruiseStartTime !== selTime) {
                if (selTime >= cruiseEndTime) {
                    alert('开始日期不能大于结束日期');
                    $("#startTime").val(formatDate(cruiseStartTime));
                    return false;
                }
                cruiseStartTime = selTime;
                stopAnimation(false);
                getGpsList(cruiseStartTime, cruiseEndTime);
            }
            if (type === 2 && cruiseEndTime !== selTime) {
                if (selTime <= cruiseStartTime) {
                    alert('结束日期不能小于开始日期');
                    $("#endTime").val(formatDate(cruiseEndTime));
                    return false;
                }
                cruiseEndTime = selTime;
                stopAnimation(false);
                getGpsList(cruiseStartTime, cruiseEndTime);
            }
        }
    });
}

// 弹出框切换
function frameSwitch($button, $frame, type) {
    let $locus = $("#locus");
    let $typhoon = $("#typhoon");
    if ($button.hasClass('active')) {
        $button.removeClass("active");
        $frame.stop();
        $frame.animate({width: "0vw"}, 300);
    } else {
        if (type === 1 && $typhoon.hasClass('active')) {
            $typhoon.removeClass("active");
            $("#typhoonFrame").animate({width: "0vw"}, 100);
        }
        if (type === 2 && $locus.hasClass('active')) {
            $locus.removeClass("active");
            $("#locusFrame").animate({width: "0vw"}, 100);
        }
        $button.addClass("active");
        $frame.stop();
        $frame.animate({width: "20.39vw"}, 300);
    }
    if ($("#hisMsg").is(':visible')) {
        $("#hisData").click();
    }
}

//获得航次信息，包括时间、里程数、航次百分比、航次名称
function getNewestCruise() {
    let timestamp = Date.parse(new Date());
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/shipCruise/queryVoyageInfo/" + getCurrentSn() + '/' + timestamp,
        dataType: "json",
        async: false,
        success: function (result) {
            //航次时间戳
            cruiseStartTime = result.startTime;
            cruiseEndTime = result.finishTime;

            sTime =  result.startTime;
            eTime = Math.round(new Date() / 1000 * 1000);
            shipName = result.shipName;
            cruiseCode = result.code;
            document.getElementById("shipNm").innerText = shipName;
            document.getElementById("cruiseCode").innerText = cruiseCode;

            // 第一次刷新
            if (count === 0) {
                //初始化GPS数据
                initGpsData();
                //初始化船向
                initHehdt();
            }

            // 获取地图GPS
            getGpsList(cruiseStartTime, Math.round(new Date() / 1000 * 1000));
        }
    });
}

//初始化GPS数据,包括经纬度、船速、utc时间
function initGpsData() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/list",
        data: {
            sn: getCurrentSn(),
            deviceCode: '032A',
            startTime: sTime,
            endTime: eTime,
            interval: 5
        },
        dataType: "json",
        async: false,
        success: function (result) {
            if (result === undefined) {
                return;
            }
            let gps = eval(result.data);
            initMap(gps[gps.length - 1][1], gps[gps.length - 1][0]);
        },
    });
}

//初始化船向
function initHehdt() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/getLatestData",
        data: {
            sn: getCurrentSn(),
            deviceCode: '042A'
        },
        dataType: "json",
        // async: false,
        success: function (result) {
            hehdt = parseInt(result.data.hehdt);
        },
    });
}

//初始化地图
function initMap(latitude, longitude) {
    BM.Config.HTTP_URL = mapHost;
    satelliteLayer = BM.tileLayer('bigemap.satellite');
    seaLayer = BM.tileLayer('bigemap.seaMap');

    map = initSatelliteMap(latitude, longitude, zoomLevel);
    satelliteLayer.addTo(map);
    nowLayer = 'satellite';
    bindMap();
}

// 切换地图图层
function changeLayer(type) {
    let latitude = map._lastCenter.lat;
    let longitude = map._lastCenter.lng;

    if (type === 'satellite' && nowLayer !== 'satellite') {
        seaLayer.remove(map);
        cleanMap();
        map = initSatelliteMap(latitude, longitude, zoomLevel);
        satelliteLayer.addTo(map);
        nowLayer = 'satellite';
    }
    if (type === 'sea' && nowLayer !== 'sea') {
        satelliteLayer.remove(map);
        cleanMap();
        map = initSeaMap(latitude, longitude, zoomLevel);
        seaLayer.addTo(map);
        nowLayer = 'sea';
    }
    drawLine();
    bindMap();
    if ($("#hisMsg").is(':visible')) {
        initHisShipMarker(gpsList[gpsList.length - 1]);
    }
}

function cleanMap() {
    if (pathPattern !== undefined) {
        pathPattern.remove();
    }
    if (shipMarker !== undefined) {
        shipMarker.remove();
    }
    stopAnimation(false);
    map.remove();
}

function bindMap() {
    //鼠标对地图的监控
    map.on("moveend", function (e) {
        zoomLevel = map.getZoom();
        moveTime = Math.round(new Date());
    });
}

// 获取历史gps数据
function getGpsList(startTime, endTime) {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/device/data/list",
        data: {
            sn: getCurrentSn(),
            deviceCode: '032A',
            startTime: startTime,
            endTime: endTime,
            interval: 5
        },
        dataType: "json",
        async: false,
        success: function (result) {
            if (result.data === undefined || result.data === '' || result.data === null) {
                return;
            }
            gpsList = [];
            allCoordinate = [];
            gpsList = eval(result.data);
            getCoordinate();

            drawLine();
            drawShipMarker(gpsList[gpsList.length - 1]);

            // 初始化起点GPS
            startPoint = [];
            startPoint.push(parseFloat(gpsList[0][1]));
            startPoint.push(parseFloat(gpsList[0][0]));
        }
    });
}

//抽取gps中的经纬度
function getCoordinate() {
    if (gpsList === undefined || gpsList.length === 0) {
        return;
    }
    allCoordinate = [];
    let attr;
    for (let i = 0; i < gpsList.length; i++) {
        attr = [];
        attr.push(parseFloat(gpsList[i][1]));
        attr.push(parseFloat(gpsList[i][0]));
        allCoordinate.push(attr);
    }
}

//画轨迹
function drawLine() {
    if (gpsList.length === 0 || allCoordinate.length === 0) {
        return;
    }
    if (pathPattern !== undefined) {
        pathPattern.remove();
    }
    //绘线
    pathPattern = BM.polylineDecorator(
        allCoordinate,
        {
            patterns: [
                {
                    offset: 5,
                    repeat: 15,
                    symbol: BM.Symbol.dash({
                        pixelSize: 9, pathOptions: {
                            color: drawColor,
                            weight: 1.5
                        }
                    })
                },
                {
                    offset: 43,
                    repeat: 70,
                    symbol: BM.Symbol.arrowHead({
                        headAngle: 40,
                        pixelSize: 15,
                        color: drawColor,
                        fillOpacity: 1,
                        weight: 1
                    })
                },
                {
                    offset: 0,
                    repeat: 70,
                    symbol: BM.Symbol.circleMarker({
                        radius: 3,
                        color: drawColor,
                        weight: 1.2,
                        fill: true,
                        fillColor: '#effffd',
                        fillOpacity: 1,
                        code: cruiseCode,
                        shipName: shipName
                    })
                },
            ]
        },
        gpsList
    ).addTo(map);

    if (document.getElementById("restore").title === "暂停自动缩放") {
        map.fitBounds(pathPattern.getBounds());
    }
}

// 画船标记
function drawShipMarker(gpsArr) {
    if (shipMarker !== undefined) {
        shipMarker.remove();
    }
    shipMarker = BM.marker([gpsArr[1], gpsArr[0]],
        {
            rotationAngle: hehdt,
            icon: BM.icon({
                // 会导致回放船向不再变动
                iconUrl: shipImg.get(drawColor),
                // 大小 x，y
                iconSize: [20, 38],
                // 偏移x,y
                iconAnchor: [20, 20],
                popupAnchor: [-3, -76],
            }),
        },
    ).addTo(map);
    shipMarker.on('mouseover', function (e) {
        let html = getShipMarkerHtml(hehdt, gpsArr);
        shipMarker.bindTooltip(html).openTooltip();
    });
}

// 船标记html
function getShipMarkerHtml(hehdt, gpsArr) {
    let speed = gpsArr[3];
    let distance = 0;
    if (gpsArr.length >= 5) {
        distance = gpsArr[4].toFixed(2);
    }

    return "<div class='popuoCss'>" +
        "<B>船名：</B>" + shipName + "\<br\>" +
        "<B>航次：</B>" + cruiseCode + "\<br\>" +
        "<B>航速：</B>" + speed + "节" + "\<br\>" +
        "<B>艏向：</B>" + hehdt + "°" + "\<br\>" +
        '<B>距初始位置：</B>' + distance + '海里\<br\>' +
        "<B>经纬度：</B>" + formatLat(gpsArr[1]) + "," + formatLong(gpsArr[0]) + "\<br\>" +
        "<B>数据时间：</B>" + transformTime(gpsArr[2]) + "\<br\></div>";
}

// 处理websocket接收的消息
function handleWsData(wsData) {
    if (wsData === undefined) {
        return;
    }

    //船向
    let hehdt;
    if (wsData[getCurrentSn() + '_042A'] !== undefined && wsData[getCurrentSn() + '_042A'] !== null) {
        hehdt = wsData[getCurrentSn() + '_042A'].hehdt;
        $('#newAttitude').text(parseInt(hehdt) + '°');
    }


    if (wsData[getCurrentSn() + '_032A'] !== undefined && wsData[getCurrentSn() + '_032A'] !== null) {
        let gpsData = wsData[getCurrentSn() + '_032A'];
        let groundRateJ = changeTwoDecimal_f(gpsData.groundRateJ, 2);
        let initialTime = parseInt(gpsData.initialTime);

        $("#longitudeValue ").text(formatLong(gpsData.longitude));
        $("#latitudeValue ").text(formatLat(gpsData.latitude));

        $("#nowGroundRateJ").text(groundRateJ + '节');
        $("#utime1").text(transformTime(initialTime));
        if (!isEmpty(gpsData)) {
            //给元素传值
            refreshGps(gpsData);
            refreshShipMarker(gpsData);
        }
    }

    if (wsData[getCurrentSn() + '_040A'] !== undefined && wsData[getCurrentSn() + '_040A'] !== null) {
        count++;
        if (count === 60 * 5) {
            getNewestCruise();
            count = 1;
        }
    }

    let currentTime = Math.round(new Date());
    //判断当前时间与操作时间的间隔，大于30秒则调取轨迹四周边界
    if (document.getElementById("restore").title === "暂停自动缩放" && currentTime > (moveTime + 1000 * 40)) {
        map.fitBounds(pathPattern.getBounds());
    }
}

//将websocket传过来的gps信息放进元素中
function refreshGps(gpsData) {
    $("#nowLongitude").html(parseFloat(gpsData.longitude).toFixed(7) + gpsData.longitudeHemisphere);
    $("#nowLatitude").html(parseFloat(gpsData.latitude).toFixed(7) + gpsData.latitudeHemisphere);
    let groundRateJ = 0;
    if (!isEmpty(gpsData.groundRateJ)) {
        groundRateJ = gpsData.groundRateJ;
    }
    $("#nowGroundRateJ").html(groundRateJ + "节");
    $("#bjTime").html(gpsData.bjTime);
}

// 刷新船只图像
function refreshShipMarker(gpsData) {
    let arr = [];
    arr.push(parseFloat(gpsData.latitude));
    arr.push(parseFloat(gpsData.longitude));

    let tempGps = [];
    tempGps.push(parseFloat(gpsData.longitude).toFixed(7));
    tempGps.push(parseFloat(gpsData.latitude).toFixed(7));
    tempGps.push(gpsData.initialBjTime);
    if (!isEmpty(gpsData.groundRateJ)) {
        tempGps.push(gpsData.groundRateJ);
    } else {
        tempGps.push(0);
    }
    // 加上离起点距离
    let dis = (map.distance(startPoint, arr)) / 1852;
    tempGps.push(dis);

    drawShipMarker(tempGps);
}

// 初始历史数据弹窗
function initHisFrame() {
    closeWs();
    getAllCruise();
    $("#startTime").val(formatDate(cruiseStartTime));
    $("#endTime").val(formatDate(cruiseEndTime));
    $("#hisLongitude").html($("#longitudeValue").html());
    $("#hisLatitude").html($("#latitudeValue").html());
    $("#hisGroundRateJ").html($("#nowGroundRateJ").html());
    $("#hisHehdt").html($("#newAttitude").html());

    initLayDate('startTime', cruiseStartTime, 1);
    initLayDate('endTime', cruiseEndTime, 2);
}

// 获取所有航次
function getAllCruise() {
    $.ajax({
        type: "GET",
        url: laputaHost + "/api/shipCruise/list/" + getCurrentSn(),
        dataType: "json",
        async: false,
        success: function (result) {
            if (isEmpty(result)) {
                return;
            }
            cruiseList = result;
            let $shipCruises = $('#shipCruises');
            $shipCruises.empty();
            for (let i = 0; i < cruiseList.length; i++) {
                document.getElementById("shipNm").innerText = cruiseList[0].shipName;
                if (cruiseList[i].code === cruiseCode) {
                    $shipCruises.append('<option  value=' + cruiseList[i].code + ' selected="selected">' + cruiseList[i].code + '</option>');
                } else {
                    $shipCruises.append('<option  value=' + cruiseList[i].code + ' >' + cruiseList[i].code + '</option>');
                }
            }
        }
    });
}
//缩放地图
//自动缩放地图
function toLessen() {
    // 缩放按钮
    $(document).ready(function () {
        let stop = 0;
        $("#restore ").click(function () {
            if (stop === 0) {
                document.getElementById("restore").title = "开始自动缩放";
                document.getElementById("restore").style =
                    " background: url(img/typhoon/start.png); background-repeat: no-repeat; border: none;background-size: 26.8px 26.8px; background-color: #fff; border: 1px solid #ccc;background-size: 95%;background-position: center center;"
                stop = 1;
            } else {
                if (gpsList.length > 2) {
                    map.fitBounds(pathPattern.getBounds());
                }
                document.getElementById("restore").title = "暂停自动缩放";
                document.getElementById("restore").style =
                    " background: url(img/typhoon/stop.png); background-repeat: no-repeat; border: none;background-size: 26.8px 26.8px; background-color: #fff; border: 1px solid #ccc;background-size: 95%;background-position: center center;"
                stop = 0;
            }

        });
    });
}

// // 地图滚轮缩放禁用和启动
// function scrollWheelDisable(dis) {
//     if (dis) {
//         map.scrollWheelZoom.disable();
//     } else {
//         map.scrollWheelZoom.enable();
//     }
// }

// 轨迹回放动画控制
function handleAnimation() {
    let $playButton = $("#playButton");
    let $slowDownButton = $("#slowDownButton");
    let $accelerateButton = $("#accelerateButton");

    if ($playButton.attr('title') === '开始' || $playButton.attr('title') === '继续') {
        if ($playButton.attr('title') === '开始') {
            initReplay();
        } else if ($playButton.attr('title') === '继续') {
            hisShipMarker.resumeMove();
        }
        $playButton.attr('title', '暂停');
        $playButton.attr('style', 'background: url(img/map/pause.png) center center/2vw 2vw no-repeat;');
        $slowDownButton.attr('disabled', false);
        $accelerateButton.attr('disabled', false);
    } else {
        hisShipMarker.pauseMove();
        $playButton.attr('title', '继续');
        $playButton.attr('style', 'background: url(img/map/start.png) center center/2vw 2vw no-repeat;');
        $slowDownButton.attr('disabled', true);
        $accelerateButton.attr('disabled', true);
    }
}

// 轨迹停止回放
function stopAnimation(reDrawMarker) {
    let $playButton = $("#playButton");
    let $slowDownButton = $("#slowDownButton");
    let $accelerateButton = $("#accelerateButton");

    if (hisShipMarker !== undefined) {
        hisShipMarker.stopMove();
        hisShipMarker.remove();
    }
    if (reDrawMarker) {
        initHisShipMarker(gpsList[0]);
    }

    if (hisPassedPath !== undefined) {
        hisPassedPath.remove();
    }

    $playButton.attr('title', '开始');
    $playButton.attr('style', 'background: url(img/map/start.png) center center/2vw 2vw no-repeat;');
    $slowDownButton.attr('disabled', true);
    $accelerateButton.attr('disabled', true);
}

// 轨迹回放减速
function slowDownAnimation() {
    if (hisShipMarker.getSpeed() <= 5000) {
        return;
    }
    hisShipMarker.setSpeed(hisShipMarker.getSpeed() - 5000);
}

// 轨迹回放加速
function accelerateAnimation() {
    hisShipMarker.setSpeed(hisShipMarker.getSpeed() + 5000);
}

// 开始回放初始化
function initReplay() {
    // 先移除原来的船只图像
    if (shipMarker !== undefined) {
        shipMarker.remove();
    }
    initHisShipMarker(gpsList[0]);

    if (hisPassedPath !== undefined) {
        hisPassedPath.remove();
    }
    hisPassedPath = BM.polyline([[]], {colorScale: '#09c', weight: 2}).addTo(map);

    hisShipMarker.on('update_position', function (e) {
        markerUpdatePosition(e);
    });

    let markerSpeed = hisShipMarker.getSpeed() === undefined ? 1000 : hisShipMarker.getSpeed();
    hisShipMarker.moveAlong(allCoordinate, markerSpeed);
}

function markerUpdatePosition(e) {
    let hehdtMove = 0;
    if (e.target.moveOptions.hehdt >= 0) {
        hehdtMove = e.target.moveOptions.hehdt + 90;
    } else if (e.target.moveOptions.hehdt < -90) {
        hehdtMove = -(e.target.moveOptions.hehdt + 90);
    } else {
        hehdtMove = -(e.target.moveOptions.hehdt) + 270;
    }
    hisPassedPath.setLatLngs(e.path);
    hisPassedPath.color;
    //每次坐标更新。然后也更新路径
    let dis = (map.distance([e.path[0].lat, e.path[0].lng], [e.path[e.path.length - 1].lat, e.path[e.path.length - 1].lng])) / 1852;
    let lng = parseFloat(e.path[e.path.length - 1].lng).toFixed(7);
    let lat = parseFloat(e.path[e.path.length - 1].lat).toFixed(7);
    let bjTime = transformTime(gpsList[e.path.length][2]);
    let speed = gpsList[e.path.length][3];
    let html = "<div class='popuoCss'>" +
        "<B>船名：</B>" + shipName + "\<br\>" +
        "<B>航次：</B>" + cruiseCode + "\<br\>" +
        "<B>航速：</B>" + speed + "节" + "\<br\>" +
        "<B>艏向：</B>" + parseFloat(hehdtMove).toFixed(2) + "°" + "\<br\>" +
        '<B>距初始位置：</B>' + parseFloat(dis).toFixed(4) + '海里\<br\>' +
        "<B>经纬度：</B>" + formatLat(lng) + "," + formatLong(lat) + "\<br\>" +
        "<B>数据时间：</B>" + bjTime + "\<br\></div>";

    hisShipMarker.bindTooltip(html).openTooltip();

    $("#hisLongitude").html(lng + "°E");
    $("#hisLatitude").html(lat + "°N");
    $("#hisGroundRateJ").html(speed + "节");
    $("#hisHehdt").html(parseFloat(hehdtMove).toFixed(2) + "°");
}

// 画船标记
function initHisShipMarker(gpsArr) {
    if (hisShipMarker !== undefined) {
        hisShipMarker.remove();
    }
    hisShipMarker = BM.marker([gpsArr[1], gpsArr[0]],
        {
            rotationAngle: 90,
            icon: BM.icon({
                // 会导致回放船向不再变动
                iconUrl: shipImg.get(drawColor),
                // 大小 x，y
                iconSize: [20, 38],
                // 偏移x,y
                iconAnchor: [20, 20],
                popupAnchor: [-3, -76],
            }),
        },
    ).addTo(map);
}

// 切换航次
function cruiseChange() {
    cruiseCode = document.getElementById("shipCruises").value;
    stopAnimation(false);
    for (let i = 0; i < cruiseList.length; i++) {
        if (cruiseList[i].code === cruiseCode) {
            cruiseStartTime = cruiseList[i].startTime;
            cruiseEndTime = cruiseList[i].finishTime;

            initLayDate('startTime', cruiseStartTime, 1);
            initLayDate('endTime', cruiseEndTime, 2);
        }
    }
    getGpsList(cruiseStartTime, cruiseEndTime);
}


