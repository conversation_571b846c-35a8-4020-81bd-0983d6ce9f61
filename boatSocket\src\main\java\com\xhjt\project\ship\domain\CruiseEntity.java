package com.xhjt.project.ship.domain;

import com.xhjt.dctcore.commoncore.domain.BaseEntity;

/**
 * 航次信息
 *
 * <AUTHOR>
 */
public class CruiseEntity extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long cruiseId;

    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 关联的船只
     */
    private String sn;

    private String shipName;

    /**
     * 船长
     */
    private String captain;

    /**
     * 航次编号
     */
    private String code;

    /**
     * 航次开始时间
     */
    private Long startTime;

    /**
     * 航次结束时间
     */
    private Long finishTime;

    /**
     * 总天数
     */
    private Integer totalDays;

    /**
     * 历史里程
     */
    private Double historyMileage;

    /**
     * 起始地
     */
    private String startPort;

    /**
     * 目的地
     */
    private String endPort;

    /**
     * 目标海域
     */
    private String seaArea;

    private Long presentTime;

    public Long getCruiseId() {
        return cruiseId;
    }

    public void setCruiseId(Long cruiseId) {
        this.cruiseId = cruiseId;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getCaptain() {
        return captain;
    }

    public void setCaptain(String captain) {
        this.captain = captain;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(Long finishTime) {
        this.finishTime = finishTime;
    }

    public Integer getTotalDays() {
        return totalDays;
    }

    public void setTotalDays(Integer totalDays) {
        this.totalDays = totalDays;
    }

    public Double getHistoryMileage() {
        return historyMileage;
    }

    public void setHistoryMileage(Double historyMileage) {
        this.historyMileage = historyMileage;
    }

    public String getStartPort() {
        return startPort;
    }

    public void setStartPort(String startPort) {
        this.startPort = startPort;
    }

    public String getEndPort() {
        return endPort;
    }

    public void setEndPort(String endPort) {
        this.endPort = endPort;
    }

    public String getSeaArea() {
        return seaArea;
    }

    public void setSeaArea(String seaArea) {
        this.seaArea = seaArea;
    }

    public Long getPresentTime() {
        return presentTime;
    }

    public void setPresentTime(Long presentTime) {
        this.presentTime = presentTime;
    }
}
