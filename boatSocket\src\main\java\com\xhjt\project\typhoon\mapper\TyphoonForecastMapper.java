package com.xhjt.project.typhoon.mapper;

import com.xhjt.project.typhoon.domain.TyphoonForecast;

import java.util.List;

/**
 * 台风路径预测 数据层
 *
 * <AUTHOR>
 */
public interface TyphoonForecastMapper {

    /**
     * 查询台风路径预测
     *
     * @param typhoonForecast 台风路径预测
     * @return 台风信息
     */
    public TyphoonForecast selectTyphoonForecast(TyphoonForecast typhoonForecast);

    /**
     * 查询台风路径预测
     *
     * @param typhoonForecast 台风路径预测
     * @return 台风集合
     */
    public List<TyphoonForecast> selectTyphoonForecastList(TyphoonForecast typhoonForecast);

    /**
     * 新增台风路径预测
     *
     * @param typhoonForecast 台风路径预测
     * @return 结果
     */
    public int addTyphoonForecast(TyphoonForecast typhoonForecast);

    /**
     * 修改台风路径预测
     *
     * @param typhoonForecast 台风路径预测
     * @return 结果
     */
    public int updateTyphoonForecast(TyphoonForecast typhoonForecast);

}